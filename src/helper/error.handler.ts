import { IServer } from "../lib/interfaces/IServer";
import * as Express from "express";

export default class ErrorHandler {
  static init(server: IServer) {
    server.app.use(
      (
        error: any,
        req: Express.Request,
        res: Express.Response,
        next: Express.NextFunction
      ) => {
        console.log(error, "In error handler");

        // Check if this is a calendar conflict error with conflicts array
        // This needs to be checked first to ensure proper handling
        if (error.conflicts && Array.isArray(error.conflicts)) {
          // Always return status 200 with success: false for conflicts
          // This ensures the frontend can properly handle the response
          const response = {
            success: false,
            message:
              error.message || "A conflict was found in your Google Calendar.",
            conflicts: error.conflicts,
          };

          return res.status(200).json(response);
        }

        // Mongo Error
        if (error.name == "MongoError") {
          if (error.code == 11000) {
            return res.status(200).json({
              success: false,
              message: `${
                Object.values(error.keyValue)[0]
              } already exists in the system`,
            });
          } else {
            return res.status(200).json({
              success: false,
              message: "Bad Request",
            });
          }
        }

        // JWT error
        if (error.name == "JsonWebTokenError") {
          return res.status(401).json({
            success: false,
            message: "Incorrect token",
          });
        }

        if (error.name == "TokenExpiredError") {
          return res.status(401).json({
            success: false,
            message: "Token expired",
          });
        }

        // For all other errors, return a JSON response with success: false
        return res.status(200).json({
          success: false,
          message: error.message || "Internal server error",
        });
      }
    );
  }
}

export const getErrorResponse = (code: any, message: any, data?: any) => {
  return {
    responseCode: 1,
    status: "error",
    message,
    errors: [
      {
        code,
        data,
      },
    ],
  };
};
