import moment from "moment";

export const removeEmpty = (obj: any) => {
  return Object.fromEntries(Object.entries(obj).filter(([_, v]) => v != null));
};

export const doIntervalsOverlap = (
  existingRecurrenceDate: any,
  requestedRecurrenceDate: any
) => {
  return (
    moment(existingRecurrenceDate.fromDate).isBefore(
      moment(requestedRecurrenceDate.toDate)
    ) &&
    moment(existingRecurrenceDate.toDate).isAfter(
      moment(requestedRecurrenceDate.fromDate)
    )
  );
};

export const removeEmptyKeys = (obj: any) => {
  if (typeof obj !== "object" || obj === null) {
    return obj;
  }

  if (Array.isArray(obj)) {
    // Remove empty keys from arrays
    return obj.filter((item) => item !== undefined);
  }

  // Remove empty keys from objects
  const result: any = {};
  for (const key in obj) {
    const value = removeEmptyKeys(obj[key]);
    if (
      value !== undefined &&
      value !== "" &&
      !(Array.isArray(value) && value.length === 0) &&
      !(typeof value === "object" && Object.keys(value).length === 0)
    ) {
      result[key] = value;
    }
  }

  return result;
};

export const mergeDate = (dateIso: any, timeIso: any) => {
  const date = moment(dateIso).toDate();
  const time = moment(timeIso).toDate();

  const datetime = new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    time.getHours(),
    time.getMinutes(),
    time.getSeconds()
  );
  return datetime;
};

export function createDailyRecurrences(
  fromDate: any,
  toDate: any,
  endDate: any,
  tillDate: any,
  countAdd: number,
  addType: "days" | "weeks" | "months",
  amount: any,
  recurrence: string
) {
  let current = moment(fromDate);
  const end = moment(endDate);
  const till = moment(tillDate);
  const fromTime = {
    hour: moment(fromDate).get("hour"),
    minute: moment(fromDate).get("minute"),
    second: moment(fromDate).get("second"),
  };
  const toTime = {
    hour: moment(toDate).get("hour"),
    minute: moment(toDate).get("minute"),
    second: moment(toDate).get("second"),
  };
  let recurrences = [];

  // Generate RRULE
  let rrule = createRRule(recurrence, endDate);

  while (current.isSameOrBefore(end, "day")) {
    let newFromDate = current.clone().set(fromTime);
    let newToDate = current.clone().set(toTime);
    // console.log({ current, newFromDate, newToDate });
    recurrences.push({
      fromDate: newFromDate.toISOString(),
      toDate: newToDate.toISOString(),
      amount: amount,
      rrule: rrule, // Add RRULE here
    });

    current.add(countAdd, addType);
  }

  return recurrences;
}

export function isOverlap(
  recurrenceDates: { fromDate: any; toDate: any }[],
  existing_dates: { fromDate: any; toDate: any }[]
) {
  for (let newRange of recurrenceDates) {
    let newStart = moment(newRange.fromDate);
    let newEnd = moment(newRange.toDate);

    for (let existingRange of existing_dates) {
      let existingStart = moment(existingRange.fromDate);
      let existingEnd = moment(existingRange.toDate);

      // Check for overlap
      if (
        (newStart.isAfter(existingStart) && newStart.isBefore(existingEnd)) || // newStart inside existing range
        (newEnd.isAfter(existingStart) && newEnd.isBefore(existingEnd)) || // newEnd inside existing range
        (existingStart.isAfter(newStart) && existingStart.isBefore(newEnd)) || // existingStart inside new range
        (existingEnd.isAfter(newStart) && existingEnd.isBefore(newEnd)) || // existingEnd inside new range
        (newStart.isSame(existingStart) && newEnd.isSame(existingEnd)) // Exact match case
      ) {
        return true; // Overlap found
      }
    }
  }
  return false; // No overlap found
}

export function findOverlaps(
  recurrenceDates: { fromDate: any; toDate: any }[],
  existing_dates: { fromDate: any; toDate: any }[]
): { startTime: string; endTime: string }[] {
  // Simple approach: return all existing dates as conflicts
  // This ensures the frontend gets all potential conflicts
  const allConflicts = existing_dates.map((date) => ({
    startTime: moment(date.fromDate).toISOString(),
    endTime: moment(date.toDate).toISOString(),
  }));

  return allConflicts;
}

export function addLeadingZeros(num: number, totalLength: number) {
  return String(num).padStart(totalLength, "0");
}

export function findOverlapDate(
  recurrenceDates: { fromDate: any; toDate: any }[],
  existing_dates: { fromDate: any; toDate: any }[]
) {
  for (let newRange of recurrenceDates) {
    let newStart = moment(newRange.fromDate);
    let newEnd = moment(newRange.toDate);

    for (let existingRange of existing_dates) {
      let existingStart = moment(existingRange.fromDate);
      let existingEnd = moment(existingRange.toDate);

      // Check for overlap
      if (
        (newStart.isAfter(existingStart) && newStart.isBefore(existingEnd)) ||
        (newEnd.isAfter(existingStart) && newEnd.isBefore(existingEnd)) ||
        (existingStart.isAfter(newStart) && existingStart.isBefore(newEnd)) ||
        (existingEnd.isAfter(newStart) && existingEnd.isBefore(newEnd))

        // newStart.isBetween(existingStart, existingEnd, null, "[]") ||
        // newEnd.isBetween(existingStart, existingEnd, null, "[]") ||
        // existingStart.isBetween(newStart, newEnd, null, "[]") ||
        // existingEnd.isBetween(newStart, newEnd, null, "[]")
      ) {
        // Return the date (as a string) on which the overlap occurs
        return newStart.format("DD-MM-YYYY");
      }
    }
  }
  return null; // No overlap found
}

export function getBestSessionMonths(data: any) {
  const monthCount: any = {};

  // Iterate over each object in the data array
  data.forEach((obj: any) => {
    // Extract the toDate value from the object
    const toDate = new Date(obj.toDate);

    // Extract the month from the toDate value
    const month = toDate.getMonth();

    // Increment count for the month in the monthCount object
    if (monthCount[month]) {
      monthCount[month]++;
    } else {
      monthCount[month] = 1;
    }
  });

  // Find the month with the maximum count
  let maxMonth = 0;
  let maxCount = 0;

  let minMonth = 0;
  let minCount = Infinity;

  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  for (const month in monthCount) {
    const monthNumber = parseInt(month); // Convert month string to number
    if (monthCount[monthNumber] > maxCount) {
      maxMonth = monthNumber;
      maxCount = monthCount[monthNumber];
    }

    // Check for minimum count
    if (monthCount[monthNumber] < minCount) {
      minMonth = monthNumber;
      minCount = monthCount[monthNumber];
    }
  }

  return {
    maxCount,
    maxMonth: monthNames[maxMonth + 1],
    minCount,
    minMonth: monthNames[minMonth + 1],
  };
}

export function getBestPaymentMonths(data: any) {
  const monthCount: any = {};

  // Iterate over each object in the data array
  data.forEach((obj: any) => {
    // Extract the toDate value from the object
    const paymentDate = new Date(obj.paymentDate);

    // Extract the month from the toDate value
    const month = paymentDate.getMonth();

    // Increment count for the month in the monthCount object
    if (monthCount[month]) {
      monthCount[month]++;
    } else {
      monthCount[month] = 1;
    }
  });

  // Find the month with the maximum count
  let maxMonth = 0;
  let maxCount = 0;

  let minMonth = 0;
  let minCount = Infinity;

  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  for (const month in monthCount) {
    const monthNumber = parseInt(month); // Convert month string to number
    if (monthCount[monthNumber] > maxCount) {
      maxMonth = monthNumber;
      maxCount = monthCount[monthNumber];
    }

    // Check for minimum count
    if (monthCount[monthNumber] < minCount) {
      minMonth = monthNumber;
      minCount = monthCount[monthNumber];
    }
  }

  return {
    maxCount,
    maxMonth: monthNames[maxMonth + 1],
    minCount,
    minMonth: monthNames[minMonth + 1],
  };
}

export function combineDateWithTime(dateInput: any, timeInput: Date) {
  // Convert JavaScript Date objects to Moment.js objects
  const dateObj = moment(dateInput);
  const timeObj = moment(timeInput);
  // Create a new Moment object from dateObj to avoid mutations
  const combinedDateTime = dateObj.clone();

  // Setting the time components from timeObj to combinedDateTime
  combinedDateTime.hour(timeObj.hour());
  combinedDateTime.minute(timeObj.minute());
  combinedDateTime.second(timeObj.second());
  combinedDateTime.millisecond(timeObj.millisecond());

  // Return the combined Moment object as a JavaScript Date object
  // Ensuring to convert it to UTC and then back to local time to neutralize time zone effects
  return combinedDateTime.toDate();
}

//Changes in data comp. with last month
export const calculatePercentageChange = (
  current: number,
  previous: number
) => {
  if (previous === 0) return current === 0 ? 0 : 100; // handle division by zero
  return ((current - previous) / previous) * 100;
};

export const getIncreaseDecrease = (current: number, previous: number) => {
  return calculatePercentageChange(current, previous);
};

export const createRRule = (
  recurrence: string,
  endDate: Date | string | any
): string => {
  const daysMap: Record<string, string> = {
    Monday: "MO",
    Tuesday: "TU",
    Wednesday: "WE",
    Thursday: "TH",
    Friday: "FR",
    Saturday: "SA",
    Sunday: "SU",
  };

  // Handle recurrence types
  if (recurrence === "Does Not Repeat") return "";

  const frequency = recurrence.includes("Every Day") ? "DAILY" : "WEEKLY";
  const interval = recurrence.includes("Two Weeks") ? 2 : 1;
  const day = Object.keys(daysMap).find((d) => recurrence.includes(d)) || "";

  // Generate RRULE
  let rrule = `RRULE:FREQ=${frequency};INTERVAL=${interval}`;
  if (day && frequency === "WEEKLY") {
    rrule += `;BYDAY=${daysMap[day]}`;
  }

  rrule += `;UNTIL=${formatEndDate(endDate)}`;
  return rrule;
};

// Utility to format end date in UTC for RRULE
const formatEndDate = (date: Date | string): string => {
  return new Date(date).toISOString().replace(/[-:]/g, "").split(".")[0] + "Z";
};

interface TimeSlot {
  startTime: string; // "HH:mm"
  endTime: string; // "HH:mm"
  duration: number;
}

interface ValidationIssue {
  day: string;
  error: "overlapping slot" | "invalid duration";
  slot: TimeSlot;
}

export const validateTherapistTimeSlots = (
  day: string,
  slots: any
): ValidationIssue[] => {
  const issues: ValidationIssue[] = [];

  // Helper to convert "HH:mm" to minutes
  const timeToMinutes = (timeStr: string): number => {
    const [hours, minutes] = timeStr.split(":").map(Number);
    return hours * 60 + minutes;
  };

  if (!Array.isArray(slots) || slots.length === 0) return [];

  // Sort slots by start time
  const sorted = [...slots].sort(
    (a, b) => timeToMinutes(a.startTime) - timeToMinutes(b.startTime)
  );

  for (let i = 0; i < sorted.length; i++) {
    const current = sorted[i];
    const start = timeToMinutes(current.startTime);
    const end = timeToMinutes(current.endTime);

    // Check if duration is valid
    const expectedDuration = end - start;
    if (expectedDuration !== current.duration) {
      issues.push({
        day,
        error: "invalid duration",
        slot: current,
      });
    }

    // Check for overlap with next slot
    if (i < sorted.length - 1) {
      const next = sorted[i + 1];
      const nextStart = timeToMinutes(next.startTime);
      const nextEnd = timeToMinutes(next.endTime);

      if (start < nextEnd && nextStart < end) {
        issues.push({
          day,
          error: "overlapping slot",
          slot: current,
        });
      }
    }
  }

  return issues;
};


export const isOverlappingSlots = (
  newSlots: { startTime: string; endTime: string }[],
  existingSlots: { startTime: string; endTime: string }[]
): { overlappingSlots: { newSlot: any; existingSlot: any }[] } => {
  // Helper function to convert HH:mm to minutes for easier comparison
  const timeToMinutes = (time: string): number => {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  };

  const overlappingSlots: { newSlot: any; existingSlot: any }[] = [];

  for (const newSlot of newSlots) {
    const newStart = timeToMinutes(newSlot.startTime);
    const newEnd = timeToMinutes(newSlot.endTime);

    for (const existingSlot of existingSlots) {
      const existingStart = timeToMinutes(existingSlot.startTime);
      const existingEnd = timeToMinutes(existingSlot.endTime);

      // Check if the new slot overlaps with any existing slot
      if (
        (newStart >= existingStart && newStart < existingEnd) || // New start time falls within existing slot
        (newEnd > existingStart && newEnd <= existingEnd) || // New end time falls within existing slot
        (newStart <= existingStart && newEnd >= existingEnd) // New slot completely encompasses existing slot
      ) {
        overlappingSlots.push({
          newSlot: newSlot,
          existingSlot: existingSlot
        });
      }
    }
  }

  return { overlappingSlots };
};
