import { Router } from "express";
import { ScriptController } from "../../controller/script.controller";

export default class TherapistRouter {
  public router: Router;

  constructor() {
    this.router = Router();
    this.routes();
  }

  public routes(): void {
    this.router.get(
      "/update-schedule/:therapistId",
      // "/update-schedule",
      ScriptController.AddCalendarEventIdAndMeetLinkIntoOnlineSchedule
    );
  }
}
