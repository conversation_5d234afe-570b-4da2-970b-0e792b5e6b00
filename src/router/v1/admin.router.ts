import { Router } from "express";
import { AdminController } from "../../controller/admin.controller";
import { TherapistController } from "../../controller/therapist.controller";
import { Validate } from "../../lib/validations/validate";
import { DeductionSchema, PayoutUpdateSchema } from "../../lib/validations/deduction.schema";
import { upload } from "../../helper/fileUpload";
import SubscriptionController from "../../controller/subscription.controller";
import { CreateSubscripion, CreateTherapistSubscripion } from "../../lib/validations/subscription.schema";
import { SubscriptionTransactionController } from "../../controller/subscriptionTransaction.controller";
import { TherapistUpdateSchema } from "../../lib/validations/therapist.schema";

export default class AdminRouter{
    public router: Router;

    constructor(){
        this.router = Router();
        this.routes()
    }

    public routes(): void{
        // GET
        this.router.get("/therapist/all", TherapistController.getAll);
        this.router.get("/therapist/count/all", TherapistController.getCountAll);
        this.router.get("/therapist/stats", AdminController.getTherapistStats);
        this.router.get("/dashboard/stats", AdminController.getStats);
        this.router.get("/sessions/all", AdminController.getAllSessions);
        this.router.get("/transactions/all", AdminController.getAllTransactions);
        this.router.get("/clients/all", AdminController.getAllClients);
        this.router.get("/schedules/all", AdminController.getSchedules);
        this.router.get("/client/transactions/all", AdminController.getClientTransactions);
        this.router.get("/invoices/all", AdminController.getInvoices);
        this.router.get("/invoices/stats", AdminController.getInvoiceStats);
        this.router.get("/payouts/all", AdminController.getPayouts);
        this.router.get("/deduction/all",  AdminController.getAllDecution)
        this.router.get("/deduction/stats",  AdminController.getDeductionStats)
        this.router.get("/payout/stats",  AdminController.getPayoutStats)
        this.router.get("/payout/pendings",  AdminController.getPendingPayouts)
        this.router.get("/payout/:id",  AdminController.getPayoutById)
        this.router.get("/subscription/get",  SubscriptionController.getSubscriptions)
        this.router.get("/subscription/get/active",  SubscriptionController.getAllActive)
        this.router.get("/subscription/get/:id",  SubscriptionController.getById)
        this.router.get("/subscription/transactions/therapist/:id",  SubscriptionTransactionController.getAllTherapistTransactions)
        this.router.get("/subscription/paid/therapist/:id",  SubscriptionController.getPaidSubscriptions)
        this.router.get("/subscription/therapist/valid/date/:therapistId",  SubscriptionTransactionController.getValidTillSubscription)
        this.router.get("/subscription/active/counts", SubscriptionTransactionController.getActiveSubscriptionCounts);
        this.router.get("/therapist/details/:id",  AdminController.getTherapistDetails)
        this.router.get("/dashboard/details",  AdminController.getDashboardDetails)
        this.router.get("/therapist/transactions/pending/:therapistId",  AdminController.getTherapistPendingTransactions)
        this.router.get("/transactions/export/csv/:therapistId",  AdminController.exportTransactionCSV)


        // this.router.get("/payouts/all", AdminController.getPayouts);

        // POST
        this.router.post("/invoice/create", AdminController.createInvoice);
        this.router.post("/payout/create/:therapistId", AdminController.createPayout)
        this.router.post("/deduction/create/:therapistId", Validate(DeductionSchema), AdminController.createDeduction)
        this.router.post("/rzp/payment/verify/:transactionId", AdminController.manuallyVerifyPayment)
        this.router.post("/export",  AdminController.makeExports)
        this.router.post("/subscription/create", Validate(CreateSubscripion),  SubscriptionController.createSubscription)
        this.router.post("/subscription/therapist/create", Validate(CreateTherapistSubscripion),  SubscriptionController.createTherapistSubscripion)
        this.router.post("/get/document", TherapistController.getS3Document); 


        // PUT
        this.router.put("/therapist/update/:id", Validate(TherapistUpdateSchema), AdminController.updateTherapist);
        this.router.put("/therapist/doc", upload.single("upload"), AdminController.updateTherapistDoc);
        this.router.put("/deduction/update/:id", Validate(DeductionSchema), AdminController.updateDeduction)
        this.router.put("/payout/update/:id", Validate(PayoutUpdateSchema), AdminController.updatePayout)
        this.router.put("/payout/rzp/transfer/:id", AdminController.makePayoutPayment)
        this.router.put("/subscription/toggle/:id", SubscriptionController.toggleActive)
        this.router.put("/subscription/transactions/refresh/:id",  SubscriptionTransactionController.refreshTransaction)
        this.router.put("/docs/upload", upload.array("upload"), AdminController.uploadTherapistDocument);
        this.router.put("/therapist/payment/toggle", AdminController.toggleTherpaistPaymentOption);


        // DELETE

        this.router.delete('/subscription/:id', SubscriptionController.deleteSubscription);
        this.router.delete("/deduction/delete/:id", AdminController.deleteDeduction)
        this.router.delete("/payout/delete/:id", AdminController.deletePayout)
        this.router.delete("/invoice/delete/:id", AdminController.deleteInvoice)
        this.router.delete("/transactions/pending/delete/:transactionId",  AdminController.deletePendingTransaction)


        // PATCH

    }
}