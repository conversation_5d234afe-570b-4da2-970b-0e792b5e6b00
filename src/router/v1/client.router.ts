import { Router } from "express";
import { Validate } from "../../lib/validations/validate";
import { authMiddleware } from "../../middleware/AuthMiddleware";
import { ClientController } from "../../controller/client.controller";
import {
  ClientSchema,
  MultipleClientsSchema,
  clientCheckSchema,
} from "../../lib/validations/client.schema";
import {
  SendOtpSchema,
  VerifyOtpSchema,
} from "../../lib/validations/therapist.schema";

export default class ClientRouter {
  public router: Router;

  constructor() {
    this.router = Router();
    this.routes();
  }

  public routes(): void {
    // GET
    this.router.get(
      "/getAllClient",
      authMiddleware(),
      ClientController.getAllClient
    );
    this.router.get(
      "/getClientById/:id",
      authMiddleware(),
      ClientController.getClientById
    );
    this.router.get(
      "/findClientByEmail",
      authMiddleware(),
      ClientController.findClientByEmail
    );

    // POST
    this.router.post(
      "/create",
      authMiddleware(),
      Validate(ClientSchema),
      ClientController.create
    );
    this.router.post(
      "/createMultipleClients",
      authMiddleware(),
      Validate(MultipleClientsSchema),
      ClientController.createMultipleClients
    );

    // PUT

    // DELETE

    // PUT
    this.router.put(
      "/otp/send",
      Validate(SendOtpSchema),
      ClientController.sendOtp
    );
    this.router.put(
      "/otp/verify",
      Validate(VerifyOtpSchema),
      ClientController.verifyOtp
    );
    this.router.post(
      "/check",
      Validate(clientCheckSchema),
      ClientController.checkClient
    );
  }
}
