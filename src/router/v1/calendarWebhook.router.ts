/**
 * Calendar Webhook Router
 * 
 * This router defines endpoints for Google Calendar webhook functionality:
 * - Webhook notification endpoint
 * - Webhook management endpoints
 * - Manual sync triggers
 * - Webhook status and statistics
 */

import { Router } from "express";
import { CalendarWebhookController } from "../../controller/calendarWebhook.controller";
import { authMiddleware } from "../../middleware/AuthMiddleware";

export class CalendarWebhookRouter {
  public router: Router;

  constructor() {
    this.router = Router();
    this.routes();
  }

  public routes(): void {
    // Public webhook endpoint (no auth required - Google Calendar calls this)
    this.router.post(
      "/calendar",
      CalendarWebhookController.handleWebhookNotification
    );

    // Authenticated endpoints for therapists
    this.router.post(
      "/setup",
      authMiddleware(),
      CalendarWebhookController.setupWebhook
    );

    this.router.post(
      "/stop",
      authMiddleware(),
      CalendarWebhookController.stopWebhook
    );

    this.router.get(
      "/status",
      authMiddleware(),
      CalendarWebhookController.getWebhookStatus
    );

    this.router.post(
      "/renew",
      authMiddleware(),
      CalendarWebhookController.renewWebhook
    );

    this.router.post(
      "/sync/manual",
      authMiddleware(),
      CalendarWebhookController.triggerManualSync
    );

    this.router.get(
      "/sync/statistics",
      authMiddleware(),
      CalendarWebhookController.getSyncStatistics
    );

    this.router.get(
      "/test/connectivity",
      authMiddleware(),
      CalendarWebhookController.testWebhookConnectivity
    );

    // Admin endpoints (would need admin auth middleware)
    this.router.get(
      "/admin/webhooks",
      authMiddleware(), // Should be adminAuthMiddleware() in production
      CalendarWebhookController.getAllActiveWebhooks
    );

    this.router.post(
      "/admin/setup-all",
      authMiddleware(), // Should be adminAuthMiddleware() in production
      CalendarWebhookController.setupAllWebhooks
    );
  }
}
