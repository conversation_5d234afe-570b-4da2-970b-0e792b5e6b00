import { Router } from "express";
import { Validate } from "../../lib/validations/validate";
import { <PERSON><PERSON>ontroller } from "../../controller/hook.controller";
import { CashFreeCreatePaymentSchema } from "../../lib/validations/cashfree.schema";

export default class HookRouter{
    public router: Router;

    constructor(){
        this.router = Router();
        this.routes()
    }

    public routes(): void{
        // GET

        // POST
        // this.router.post("/create-payment-link", Validate(CashFreeCreatePaymentSchema), HookController.createPaymentLink);
        // this.router.post("/create-stripe-payment-link/:therapistId", HookController.createStipePaymentLink);
        // this.router.post("/payment-success", HookController.paymentSuccess);
        // this.router.post("/get-all-stripe-payments", HookController.getAllStripePayments);
        // this.router.post("/batch-transfer", HookController.batchTransfer);
        // this.router.post("/refundPayment", HookController.refundPayment); 
        // this.router.post("/payment-refund", HookController.paymentRefund); 

        this.router.post("/rzp/paid/full", HookController.rzpFullPaid);


        // PUT

        // DELETE

    }
}