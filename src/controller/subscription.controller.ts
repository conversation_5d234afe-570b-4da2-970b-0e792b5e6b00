import express from 'express';
import SubscriptionService from '../services/subscription.service';
import { Response } from '../util/response';
import { SubscriptionStatusEnum } from '../models/Subscription.model';
import TherapistSubscriptionService from '../services/therapistsubscription.service';
import { TherapistService } from '../services/therapist.service';
import moment from 'moment';

export default class SubscriptionController {
    static async createSubscription(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            let payload = req.body;
            payload.createdBy = req.admin._id;
            const subscription = await SubscriptionService.create(payload);
            if(!subscription) return res.status(500).send(new Response({}, 'Internal Server Error', 500))

            res.send(new Response(subscription, 'Subscription Created', 200))

        } catch (error) {
            next(error);
        }
    }

    // In SubscriptionController
    static async deleteSubscription(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const id = req.params.id;
            const subscription = await SubscriptionService.getBy_id(id, false);
            if (!subscription) return res.status(404).send(new Response({}, 'Subscription Not Found', 404));

            await SubscriptionService.deleteSubscription(id);
            res.send(new Response({}, 'Subscription Deleted Successfully', 200));
        } catch (error) {
            next(error);
        }
    }


    static async getSubscriptions(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {

           
            const pageNumber: any = Number(req.query.pageNumber) || 1;
            const pageSize: any = Number(req.query.pageSize) || 20;
            const skip = (pageNumber - 1) * pageSize;


            const subscriptions = await SubscriptionService.get(pageSize, skip);
            if(!subscriptions) return res.status(500).send(new Response({}, 'Internal Server Error', 500))

            const count = await SubscriptionService.count();
            res.send(new Response({subscriptions, count}, 'Subscription Fetched', 200))

        } catch (error) {
            next(error);
        }
    }

    static async getById(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {

            const id = req.params.id;
            const subscription = await SubscriptionService.getBy_id(id, false);
            if(!subscription) return res.status(404).send(new Response({}, 'Not Found', 404))

            res.send(new Response({subscription}, 'Subscription Created', 200))

        } catch (error) {
            next(error);
        }
    }

    static async getActiveSubscriptions(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const subscriptions = await SubscriptionService.allActive();
            if(!subscriptions) return res.status(404).send(new Response({}, 'Internal Server Error', 404))

            res.send(new Response({subscriptions}, 'Subscription Fetched', 200))

        } catch (error) {
            next(error);
        }
    }

    static async getAllActive(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const subscriptions = await SubscriptionService.allActive();
            if(!subscriptions) return res.status(404).send(new Response({}, 'Internal Server Error', 404))

            res.send(new Response({subscriptions}, 'Subscription Fetched', 200))

        } catch (error) {
            next(error);
        }
    }

    static async toggleActive(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const id = req.params.id;
            const subscription = await SubscriptionService.getBy_id(id, false);
            if(!subscription) return res.status(404).send(new Response({}, 'Not Found', 404))

            subscription.status = subscription.status === SubscriptionStatusEnum.ACTIVE ? SubscriptionStatusEnum.INACTIVE : SubscriptionStatusEnum.ACTIVE;
            await subscription.save();
            res.send(new Response({subscription}, 'Subscription Updated', 200))

        } catch (error) {
            next(error);
        }
    }

    static async getPaidSubscriptions(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const therapistId: any = req?.therapist?._id || req.params.id;
            const subscriptions = await TherapistSubscriptionService.getPaidByTherapistId(therapistId);
            if(!subscriptions) return res.status(404).send(new Response({}, 'Internal Server Error', 404))

            res.send(new Response({subscriptions}, 'Subscription Fetched', 200))

        } catch (error) {
            next(error);
        }
    }

    static async createTherapistSubscripion(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {


            const { subscriptionId, therapistId, daysLeft } = req.body;

            const subscription = await SubscriptionService.getBy_id(subscriptionId, false);
            if(!subscription) return res.status(404).send(new Response({}, 'Therapist Not Found', 404))
            const therapist = await TherapistService.getTherapist(therapistId);
            if(!therapist) return res.status(404).send(new Response({}, 'Therapist Not Found', 404))

            const validFrom = await TherapistSubscriptionService.getActiveTillSubscriptionLastDate(therapistId);

            let validTill = moment(validFrom);
            if(daysLeft) {
                validTill.add(daysLeft, 'days');
            }
            else {
                validTill.add(subscription.validDays, 'days');
            }

            const payload = {
                therapistId,
                subscriptionId,
                validTill: validTill.toDate(),
                validFrom: validFrom,
                adminId: req.admin._id
            }

            const therapistSubscription = await TherapistSubscriptionService.create(payload);

            if(!therapistSubscription) return res.status(500).send(new Response({}, 'Unable to create Subscription', 500))

            res.send(new Response({therapistSubscription}, 'Subscription Created', 200))

        } catch (error) {
            next(error);
        }
    }

}