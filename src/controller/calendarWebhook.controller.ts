/**
 * Calendar Webhook Controller
 *
 * This controller handles Google Calendar webhook endpoints:
 * - Receives webhook notifications from Google Calendar
 * - Manages webhook setup and lifecycle
 * - Provides webhook status and management endpoints
 * - Handles calendar series rescheduling triggered by webhooks
 */

import express from "express";
import { CalendarWebhookService } from "../services/calendarWebhook.service";
import { OptimizedCalendarSyncService } from "../services/optimizedCalendarSync.service";
import { Response } from "../util/response";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { ScheduleStatus } from "../models/Schedule.model";

export class CalendarWebhookController {

  /**
   * Handle incoming Google Calendar webhook notifications
   */
  static async handleWebhookNotification(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      // Enhanced logging for webhook debugging
      console.log(`🔔 Webhook notification received at ${new Date().toISOString()}`);
      console.log(`📋 Headers:`, {
        'x-goog-channel-id': req.headers['x-goog-channel-id'],
        'x-goog-resource-state': req.headers['x-goog-resource-state'],
        'x-goog-resource-id': req.headers['x-goog-resource-id'],
        'x-goog-resource-uri': req.headers['x-goog-resource-uri'],
        'x-goog-channel-token': req.headers['x-goog-channel-token'],
        'x-goog-event-type': req.headers['x-goog-event-type'],
        'x-goog-event-id': req.headers['x-goog-event-id'],
        'content-type': req.headers['content-type'],
        'user-agent': req.headers['user-agent']
      });

      if (req.body && Object.keys(req.body).length > 0) {
        console.log(`📄 Body:`, req.body);
      }

      // Google Calendar sends notifications via POST with specific headers
      const result = await CalendarWebhookService.processWebhookNotification(
        req.headers,
        req.body
      );

      if (result.success) {
        console.log(`✅ Webhook notification processed successfully`);
        // Respond with 200 to acknowledge receipt
        res.status(200).send("OK");
      } else {
        console.error(`❌ Webhook processing failed:`, result.message);
        res.status(400).send(result.message);
      }
    } catch (error: any) {
      console.error(`💥 Error in webhook handler:`, error.message);
      console.error(`Stack trace:`, error.stack);
      res.status(500).send("Internal server error");
    }
  }

  /**
   * Setup webhook for a therapist
   */
  static async setupWebhook(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;

      const channel = await CalendarWebhookService.setupWebhook(therapistId);

      if (channel) {
        res.status(200).send(
          new Response(
            {
              channelId: channel.id,
              expiration: channel.expiration,
              isActive: channel.isActive
            },
            "Webhook setup successful",
            200
          )
        );
      } else {
        res.status(400).send(
          new Response(
            null,
            "Failed to setup webhook",
            400
          )
        );
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * Stop webhook for a therapist
   */
  static async stopWebhook(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;

      // Find active webhook for this therapist
      const CalendarWebhookModel = require("../models/CalendarWebhook.model").default;
      const activeWebhook = await CalendarWebhookModel.findOne({
        therapistId: therapistId,
        isActive: true
      });

      if (!activeWebhook) {
        return res.status(404).send(
          new Response(
            null,
            "No active webhook found",
            404
          )
        );
      }

      // Stop the webhook using the found channelId
      const success = await CalendarWebhookService.stopWebhook(activeWebhook.channelId);

      if (success) {
        res.status(200).send(
          new Response(
            {
              stopped: true,
              channelId: activeWebhook.channelId
            },
            "Webhook stopped successfully",
            200
          )
        );
      } else {
        res.status(400).send(
          new Response(
            null,
            "Failed to stop webhook",
            400
          )
        );
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get webhook status for a therapist
   */
  static async getWebhookStatus(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const CalendarWebhookModel = require("../models/CalendarWebhook.model").default;

      const therapistWebhooks = await CalendarWebhookModel.find({
        therapistId: therapistId,
        isActive: true
      });

      res.status(200).send(
        new Response(
          {
            webhooks: therapistWebhooks,
            count: therapistWebhooks.length,
            hasActiveWebhook: therapistWebhooks.length > 0
          },
          "Webhook status retrieved successfully",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Trigger manual sync for a therapist
   */
  static async triggerManualSync(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const { incremental = true, maxResults = 100 } = req.body;

      const syncResult = await OptimizedCalendarSyncService.performIncrementalSync(
        therapistId,
        {
          incremental,
          maxResults
        }
      );

      res.status(200).send(
        new Response(
          {
            success: syncResult.success,
            syncedEvents: syncResult.syncedEvents,
            updatedEvents: syncResult.updatedEvents,
            createdEvents: syncResult.createdEvents,
            errors: syncResult.errors,
            conflicts: syncResult.conflicts
          },
          syncResult.success ? "Manual sync completed successfully" : "Manual sync completed with errors",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get syncable events categorized by type (new sessions vs rescheduled sessions)
   */
  static async getSyncableEvents(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const { maxResults = 100 } = req.query;

      // Get Google Calendar events
      const googleCalendarData = await GoogleCalendarService.findByTherapist(therapistId);
      if (!googleCalendarData) {
        return res.status(404).send(
          new Response(
            null,
            "No Google Calendar data found for this therapist",
            404
          )
        );
      }

      // Get events from Google Calendar
      const events = await GoogleCalendarService.eventByDate(
        therapistId,
        parseInt(maxResults as string),
        new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        new Date(Date.now() + 180 * 24 * 60 * 60 * 1000)  // Next 6 months
      );

      if (!events || events.length === 0) {
        return res.status(200).send(
          new Response(
            {
              newSessions: [],
              rescheduledSessions: [],
              totalEvents: 0
            },
            "No events found in Google Calendar",
            200
          )
        );
      }

      // Categorize events
      const categorizedEvents = await this.categorizeEvents(therapistId, events);

      res.status(200).send(
        new Response(
          categorizedEvents,
          "Syncable events retrieved successfully",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Categorize events into new sessions and rescheduled sessions
   */
  private static async categorizeEvents(therapistId: string, events: any[]) {
    const newSessions: any[] = [];
    const rescheduledSessions: any[] = [];
    const existingSchedules = await ScheduleDao.getScheduleByTherapistId(therapistId);

    for (const event of events) {
      // Skip events without attendees
      if (!event.attendees || event.attendees.length === 0) {
        continue;
      }

      // Check if this event already exists in our database
      const existingCalendarEvent = await CalendarEventDao.findByGoogleEventId(event.id);
      if (existingCalendarEvent) {
        continue; // Skip events that are already synced
      }

      // Check if this is a rescheduled session
      const isRescheduled = await this.isRescheduledEvent(event, existingSchedules);

      if (isRescheduled) {
        rescheduledSessions.push({
          id: event.id,
          summary: event.summary,
          start: event.start,
          end: event.end,
          attendees: event.attendees,
          description: event.description,
          location: event.location,
          originalSession: isRescheduled.originalSession,
          rescheduledFrom: isRescheduled.rescheduledFrom
        });
      } else {
        newSessions.push({
          id: event.id,
          summary: event.summary,
          start: event.start,
          end: event.end,
          attendees: event.attendees,
          description: event.description,
          location: event.location
        });
      }
    }

    return {
      newSessions,
      rescheduledSessions,
      totalEvents: events.length,
      newSessionsCount: newSessions.length,
      rescheduledSessionsCount: rescheduledSessions.length
    };
  }

  /**
   * Check if an event is a rescheduled version of an existing session
   */
  private static async isRescheduledEvent(event: any, existingSchedules: any[]): Promise<any> {
    const eventAttendees = (event.attendees || []).map((a: any) => a.email).sort();

    for (const schedule of existingSchedules) {
      for (const recurrenceDate of schedule.recurrenceDates) {
        // Skip cancelled sessions
        if (recurrenceDate.status === ScheduleStatus.CANCELLED) {
          continue;
        }

        // Check if attendees match
        const scheduleAttendees = [schedule.email, ...(schedule.additionalEmails || [])].sort();
        if (JSON.stringify(eventAttendees) !== JSON.stringify(scheduleAttendees)) {
          continue;
        }

        // Check if the session is already marked as rescheduled
        if (recurrenceDate.status !== ScheduleStatus.RESCHEDULED) {
          continue;
        }

        // Check if the event has a detached ID pattern (indicates rescheduling in Google Calendar)
        const detachedIdPattern = /^[a-zA-Z0-9]+_\d{8}T\d{6}Z/;
        if (!detachedIdPattern.test(event.id)) {
          continue;
        }

        // Check if the event summary/title is similar
        const eventSummary = (event.summary || '').toLowerCase();
        const scheduleName = (schedule.name || '').toLowerCase();
        const scheduleDescription = (schedule.description || '').toLowerCase();

        if (eventSummary && (
          eventSummary.includes(scheduleName) ||
          scheduleName.includes(eventSummary) ||
          eventSummary.includes(scheduleDescription) ||
          scheduleDescription.includes(eventSummary)
        )) {
          return {
            originalSession: {
              scheduleId: schedule._id,
              recurrenceDateId: recurrenceDate._id,
              originalFromDate: recurrenceDate.fromDate,
              originalToDate: recurrenceDate.toDate,
              clientEmail: schedule.email,
              clientName: schedule.name
            },
            rescheduledFrom: recurrenceDate.fromDate
          };
        }

        // Check if there's an existing calendar event with similar base ID
        const baseEventId = event.id.split('_')[0];
        const existingCalendarEvent = await CalendarEventDao.findByGoogleEventId(baseEventId);
        if (existingCalendarEvent && existingCalendarEvent.scheduleId.toString() === schedule._id.toString()) {
          return {
            originalSession: {
              scheduleId: schedule._id,
              recurrenceDateId: recurrenceDate._id,
              originalFromDate: recurrenceDate.fromDate,
              originalToDate: recurrenceDate.toDate,
              clientEmail: schedule.email,
              clientName: schedule.name
            },
            rescheduledFrom: recurrenceDate.fromDate
          };
        }
      }
    }

    return null;
  }

  /**
   * Sync only rescheduled sessions for a therapist
   */
  static async syncRescheduledSessions(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const { eventIds } = req.body; // Array of specific event IDs to sync

      if (!eventIds || !Array.isArray(eventIds) || eventIds.length === 0) {
        return res.status(400).send(
          new Response(
            null,
            "Event IDs array is required",
            400
          )
        );
      }

      // Get Google Calendar events for the specific IDs
      const googleCalendarData = await GoogleCalendarService.findByTherapist(therapistId);
      if (!googleCalendarData) {
        return res.status(404).send(
          new Response(
            null,
            "No Google Calendar data found for this therapist",
            404
          )
        );
      }

      let syncedCount = 0;
      let errors: any[] = [];

      // Process each event ID
      for (const eventId of eventIds) {
        try {
          // Get the specific event from Google Calendar
          const event = await GoogleCalendarService.getEventById(therapistId, eventId);
          if (!event) {
            errors.push({
              eventId,
              error: "Event not found in Google Calendar"
            });
            continue;
          }

          // Check if this is a rescheduled session and update it
          const existingSchedules = await ScheduleDao.getScheduleByTherapistId(therapistId);
          const isRescheduled = await this.isRescheduledEvent(event, existingSchedules);

          if (isRescheduled) {
            // Update the rescheduled session
            const schedule = await ScheduleDao.getScheduleById(isRescheduled.originalSession.scheduleId);
            if (schedule) {
              const recurrenceDate = schedule.recurrenceDates.find(
                (rd: any) => rd._id.toString() === isRescheduled.originalSession.recurrenceDateId.toString()
              );

              if (recurrenceDate) {
                // Update the recurrence date with new timing
                recurrenceDate.fromDate = new Date(event.start.dateTime || event.start.date);
                recurrenceDate.toDate = new Date(event.end.dateTime || event.end.date);
                recurrenceDate.status = ScheduleStatus.CONFIRMED; // Change from rescheduled to confirmed
                recurrenceDate.syncStatus = true;

                // Update the other field to track the rescheduling
                if (!recurrenceDate.other) {
                  recurrenceDate.other = {};
                }
                recurrenceDate.other.isRescheduled = true;
                recurrenceDate.other.rescheduledAt = new Date();
                recurrenceDate.other.rescheduledFrom = 'manual_sync';

                await schedule.save();

                // Create or update calendar event record
                let calendarEvent = await CalendarEventDao.findByScheduleAndRecurrenceId(schedule._id, recurrenceDate._id);

                if (!calendarEvent) {
                  // Create new calendar event record
                  const calendarEventData = {
                    therapistId: therapistId,
                    scheduleId: schedule._id,
                    scheduleRecId: recurrenceDate._id,
                    etag: event.etag,
                    id: event.id,
                    status: event.status,
                    htmlLink: event.htmlLink,
                    created: new Date(event.created),
                    updated: new Date(event.updated),
                    summary: event.summary,
                    description: event.description,
                    location: event.location,
                    creator: event.creator,
                    organizer: event.organizer,
                    start: event.start,
                    end: event.end,
                    iCalUID: event.iCalUID,
                    sequence: event.sequence,
                    attendees: event.attendees,
                    reminders: event.reminders,
                    eventType: event.eventType,
                    hangoutLink: event.hangoutLink,
                    conferenceData: event.conferenceData,
                    visibility: event.visibility
                  };

                  calendarEvent = await CalendarEventDao.createCalendarEvent(calendarEventData);
                  if (calendarEvent) {
                    recurrenceDate.calenderEventId = calendarEvent._id;
                    await schedule.save();
                  }
                } else {
                  // Update existing calendar event
                  calendarEvent.id = event.id; // Update to new detached ID
                  calendarEvent.start = event.start;
                  calendarEvent.end = event.end;
                  calendarEvent.updated = new Date(event.updated);
                  calendarEvent.summary = event.summary;
                  calendarEvent.description = event.description;
                  calendarEvent.location = event.location;
                  calendarEvent.attendees = event.attendees;
                  await calendarEvent.save();
                }

                syncedCount++;
              }
            }
          } else {
            errors.push({
              eventId,
              error: "Event is not a rescheduled session or no matching session found"
            });
          }
        } catch (error: any) {
          errors.push({
            eventId,
            error: error.message
          });
        }
      }

      res.status(200).send(
        new Response(
          {
            syncedCount,
            totalRequested: eventIds.length,
            errors,
            success: errors.length === 0
          },
          `Rescheduled sessions sync completed. ${syncedCount} sessions synced successfully.`,
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Start automatic sync for all therapists
   */
  static async startAutomaticSync(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const { intervalMinutes = 3 } = req.body;

      const { AutomaticCalendarSyncService } = require('../services/automaticCalendarSync.service');
      AutomaticCalendarSyncService.startAutomaticSync(intervalMinutes);

      res.status(200).send(
        new Response(
          {
            started: true,
            intervalMinutes,
            message: `Automatic sync started - will run every ${intervalMinutes} minutes`
          },
          "Automatic sync started successfully",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Stop automatic sync
   */
  static async stopAutomaticSync(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const { AutomaticCalendarSyncService } = require('../services/automaticCalendarSync.service');
      AutomaticCalendarSyncService.stopAutomaticSync();

      res.status(200).send(
        new Response(
          { stopped: true },
          "Automatic sync stopped successfully",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get automatic sync status
   */
  static async getAutomaticSyncStatus(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const { AutomaticCalendarSyncService } = require('../services/automaticCalendarSync.service');
      const status = AutomaticCalendarSyncService.getSyncStatus();

      res.status(200).send(
        new Response(
          status,
          "Automatic sync status retrieved successfully",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Trigger immediate sync for all therapists
   */
  static async triggerImmediateSync(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const { AutomaticCalendarSyncService } = require('../services/automaticCalendarSync.service');
      const result = await AutomaticCalendarSyncService.triggerImmediateSync();

      res.status(200).send(
        new Response(
          result,
          result.success ? "Immediate sync completed successfully" : "Immediate sync completed with errors",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }





  /**
   * Renew webhook for a therapist
   */
  static async renewWebhook(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;

      // Find active webhook for this therapist
      const CalendarWebhookModel = require("../models/CalendarWebhook.model").default;
      const activeWebhook = await CalendarWebhookModel.findOne({
        therapistId: therapistId,
        isActive: true
      });

      if (!activeWebhook) {
        return res.status(404).send(
          new Response(
            null,
            "No active webhook found to renew",
            404
          )
        );
      }

      // Stop the old webhook
      await CalendarWebhookService.stopWebhook(activeWebhook.channelId);

      // Create a new webhook
      const newChannel = await CalendarWebhookService.setupWebhook(therapistId);

      if (newChannel) {
        res.status(200).send(
          new Response(
            {
              renewed: true,
              oldChannelId: activeWebhook.channelId,
              newChannelId: newChannel.id,
              expiration: newChannel.expiration,
              isActive: newChannel.isActive
            },
            "Webhook renewed successfully",
            200
          )
        );
      } else {
        res.status(400).send(
          new Response(
            null,
            "Failed to renew webhook",
            400
          )
        );
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get sync statistics for a therapist
   */
  static async getSyncStatistics(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const CalendarWebhookModel = require("../models/CalendarWebhook.model").default;

      const webhook = await CalendarWebhookModel.findOne({
        therapistId: therapistId,
        isActive: true
      });

      if (!webhook) {
        return res.status(404).send(
          new Response(
            null,
            "No active webhook found",
            404
          )
        );
      }

      res.status(200).send(
        new Response(
          {
            totalSyncs: webhook.syncStatistics.totalSyncs,
            successfulSyncs: webhook.syncStatistics.successfulSyncs,
            failedSyncs: webhook.syncStatistics.failedSyncs,
            lastSyncAt: webhook.syncStatistics.lastSyncAt,
            eventsProcessed: webhook.syncStatistics.eventsProcessed,
            conflictsDetected: webhook.syncStatistics.conflictsDetected,
            conflictsResolved: webhook.syncStatistics.conflictsResolved,
            notificationCount: webhook.notificationCount,
            lastNotificationAt: webhook.lastNotificationAt
          },
          "Sync statistics retrieved successfully",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Test webhook connectivity and debugging
   */
  static async testWebhookConnectivity(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const CalendarWebhookModel = require("../models/CalendarWebhook.model").default;

      // Get webhook status
      const activeWebhooks = await CalendarWebhookModel.find({
        therapistId,
        isActive: true
      }).sort({ createdAt: -1 });

      const testResults = {
        webhookStatus: {
          hasActiveWebhook: activeWebhooks.length > 0,
          activeWebhooks: activeWebhooks.map((w: any) => ({
            channelId: w.channelId,
            expiration: w.expiration,
            isExpired: w.isExpired(),
            isExpiringSoon: w.isExpiringSoon(),
            lastNotificationAt: w.lastNotificationAt,
            notificationCount: w.notificationCount,
            syncStatistics: w.syncStatistics
          }))
        },
        connectivity: {
          webhookUrl: `${process.env.WEBHOOK_BASE_URL || process.env.FRONTEND_BASEURL}/api/v1/webhook/calendar`,
          timestamp: new Date().toISOString(),
          environment: process.env.NODE_ENV || 'development'
        },
        troubleshooting: {
          commonIssues: [
            "Webhook URL not accessible from Google servers",
            "Webhook expired and needs renewal",
            "Google Calendar not sending notifications for series changes",
            "Firewall blocking incoming webhook requests",
            "ngrok tunnel not running (in development)"
          ],
          nextSteps: [
            "1. Check if webhook URL is publicly accessible",
            "2. Verify webhook hasn't expired",
            "3. Test by making a small change in Google Calendar",
            "4. Check server logs for incoming webhook notifications",
            "5. Ensure ngrok is running and URL is correct (development)"
          ],
          debuggingTips: [
            "Monitor server logs when making calendar changes",
            "Check webhook notification count increases after changes",
            "Verify webhook expiration date",
            "Test webhook URL accessibility from external tools"
          ]
        }
      };

      res.status(200).send(
        new Response(
          testResults,
          "Webhook connectivity test completed",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all active webhooks (admin endpoint)
   */
  static async getAllActiveWebhooks(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const activeWebhooks = await CalendarWebhookService.getActiveWebhooks();

      res.status(200).send(
        new Response(
          {
            webhooks: activeWebhooks,
            count: activeWebhooks.length
          },
          "Active webhooks retrieved successfully",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Setup webhooks for all therapists (admin endpoint)
   */
  static async setupAllWebhooks(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      await CalendarWebhookService.setupWebhooksForAllTherapists();

      res.status(200).send(
        new Response(
          { initiated: true },
          "Webhook setup initiated for all therapists",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reschedule calendar series - handles webhook-triggered series rescheduling
   */
  static async rescheduleCalendarSeries(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const {
        calendarSeriesId,
        calendarEventId,
        newStartTime,
        newEndTime,
        rescheduleType = 'entire_series' // 'single_session', 'partial_series', 'entire_series'
      } = req.body;

      if (!calendarSeriesId || !newStartTime || !newEndTime) {
        return res.status(400).json({
          success: false,
          message: "Calendar series ID, new start time, and new end time are required",
        });
      }

      // Find all sessions in the series
      const sessions = await ScheduleDao.findByCalendarSeriesId(calendarSeriesId);
      if (!sessions || sessions.length === 0) {
        return res.status(404).json({
          success: false,
          message: "No sessions found for the specified calendar series",
        });
      }

      let updatedSessions = 0;
      let errors = [];

      // Handle different reschedule types
      switch (rescheduleType) {
        case 'single_session':
          // Reschedule only the specific session
          const singleSession = sessions.find(s =>
            s.recurrenceDates.some(rd => rd.calenderEventId?.toString() === calendarEventId)
          );
          if (singleSession) {
            const recDate = singleSession.recurrenceDates.find(rd =>
              rd.calenderEventId?.toString() === calendarEventId
            );
            if (recDate) {
              recDate.fromDate = new Date(newStartTime);
              recDate.toDate = new Date(newEndTime);
              recDate.status = ScheduleStatus.RESCHEDULED;
              await singleSession.save();
              updatedSessions = 1;
            }
          }
          break;

        case 'entire_series':
          // Reschedule sessions from the specified event onwards
          for (const session of sessions) {
            try {
              // Find the specific recurrence date that matches the calendarEventId
              const targetRecurrence = session.recurrenceDates.find(rd =>
                rd.calenderEventId?.toString() === calendarEventId
              );

              if (!targetRecurrence) {
                console.warn(`Target recurrence not found for calendarEventId: ${calendarEventId}`);
                continue;
              }

              // Calculate time difference based on the target recurrence, not the first one
              const timeDiff = new Date(newStartTime).getTime() - targetRecurrence.fromDate.getTime();

              // Sort recurrence dates by date to ensure proper ordering
              const sortedRecurrences = [...session.recurrenceDates].sort((a, b) =>
                new Date(a.fromDate).getTime() - new Date(b.fromDate).getTime()
              );

              // Find the index of the target recurrence in the sorted array
              const targetIndex = sortedRecurrences.findIndex(rd =>
                rd._id.toString() === targetRecurrence._id.toString()
              );

              if (targetIndex === -1) {
                console.warn(`Target recurrence index not found for: ${targetRecurrence._id}`);
                continue;
              }

              // Only reschedule sessions from the target session onwards
              for (let i = targetIndex; i < sortedRecurrences.length; i++) {
                const recDate = sortedRecurrences[i];
                const newFromDate = new Date(recDate.fromDate.getTime() + timeDiff);
                const newToDate = new Date(recDate.toDate.getTime() + timeDiff);

                // Ensure the new date is not in the past
                if (newFromDate.getTime() >= Date.now()) {
                  recDate.fromDate = newFromDate;
                  recDate.toDate = newToDate;
                  recDate.status = ScheduleStatus.RESCHEDULED;

                  console.log(`Rescheduled session ${recDate._id} from ${recDate.fromDate} to ${newFromDate}`);
                } else {
                  console.warn(`Skipping past date for session ${recDate._id}: ${newFromDate}`);
                }
              }

              await session.save();
              updatedSessions++;
            } catch (error: any) {
              console.error(`Error rescheduling session ${session._id}:`, error.message);
              errors.push({
                sessionId: session._id,
                error: error.message
              });
            }
          }
          break;

        case 'partial_series':
          // Reschedule sessions from the specified event onwards (same logic as entire_series)
          for (const session of sessions) {
            try {
              // Find the specific recurrence date that matches the calendarEventId
              const targetRecurrence = session.recurrenceDates.find(rd =>
                rd.calenderEventId?.toString() === calendarEventId
              );

              if (!targetRecurrence) {
                console.warn(`Target recurrence not found for calendarEventId: ${calendarEventId}`);
                continue;
              }

              // Calculate time difference based on the target recurrence
              const timeDiff = new Date(newStartTime).getTime() - targetRecurrence.fromDate.getTime();

              // Sort recurrence dates by date to ensure proper ordering
              const sortedRecurrences = [...session.recurrenceDates].sort((a, b) =>
                new Date(a.fromDate).getTime() - new Date(b.fromDate).getTime()
              );

              // Find the index of the target recurrence in the sorted array
              const targetIndex = sortedRecurrences.findIndex(rd =>
                rd._id.toString() === targetRecurrence._id.toString()
              );

              if (targetIndex === -1) {
                console.warn(`Target recurrence index not found for: ${targetRecurrence._id}`);
                continue;
              }

              // Only reschedule sessions from the target session onwards
              for (let i = targetIndex; i < sortedRecurrences.length; i++) {
                const recDate = sortedRecurrences[i];
                const newFromDate = new Date(recDate.fromDate.getTime() + timeDiff);
                const newToDate = new Date(recDate.toDate.getTime() + timeDiff);

                // Ensure the new date is not in the past
                if (newFromDate.getTime() >= Date.now()) {
                  recDate.fromDate = newFromDate;
                  recDate.toDate = newToDate;
                  recDate.status = ScheduleStatus.RESCHEDULED;

                  console.log(`Rescheduled session ${recDate._id} from ${recDate.fromDate} to ${newFromDate}`);
                } else {
                  console.warn(`Skipping past date for session ${recDate._id}: ${newFromDate}`);
                }
              }

              await session.save();
              updatedSessions++;
            } catch (error: any) {
              console.error(`Error rescheduling session ${session._id}:`, error.message);
              errors.push({
                sessionId: session._id,
                error: error.message
              });
            }
          }
          break;

        default:
          return res.status(400).json({
            success: false,
            message: "Invalid reschedule type",
          });
      }

      res.status(200).json({
        success: true,
        message: `Successfully rescheduled ${updatedSessions} session(s)`,
        data: {
          updatedSessions,
          rescheduleType,
          errors: errors.length > 0 ? errors : undefined
        }
      });

    } catch (error) {
      console.error("Error in rescheduleCalendarSeries:", error);
      next(error);
    }
  }
}
