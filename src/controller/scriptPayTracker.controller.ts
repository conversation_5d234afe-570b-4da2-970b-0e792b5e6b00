import express from "express";
import { scriptPayTrackerService } from "../services/scriptPayTracker.service";

export class scriptPayTrackerController {
  static async generatePayTracker(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.params.therapistId;

      if (!therapistId) {
        return res
          .status(400)
          .json({ success: false, message: "Therapist ID is required" });
      }

      const session =
        await scriptPayTrackerService.findSessionWithoutPayTracker(
          String(therapistId)
        );

      return res.status(200).json({
        success: true,
        message: "payTracker generated successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  static async addLocationSchedule(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.params.therapistId;

      if (!therapistId) {
        return res.status(400).json({
          success: false,
          message: "Therapist ID is required",
        });
      }
      const schedule = await scriptPayTrackerService.addLocationAndRecurrence(
        String(therapistId)
      );

      return res.status(200).json({
        success: true,
        message: "location added successfully",
      });
    } catch (error) {
      next(error);
    }
  }
}
