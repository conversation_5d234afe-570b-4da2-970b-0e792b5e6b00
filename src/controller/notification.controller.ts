import express from 'express';
import { Response } from '../util/response';
import { NotificationService } from '../services/notification.service';

export class NoticationController {
    static async create(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const payload = req.body;
            const notification = await NotificationService.create(payload);
            if (!notification) {
                return res.status(404).json({ success: false, message: "Notification no created. " })
            }
            res.send(new Response({ notification }, "notification created successfully. ", 200))

        }
        catch (error) {
            next(error)
        }
    }

    static async update(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const notificationId = req.params.id;
            const payload = req.body;
            const notification = await NotificationService.update(notificationId, payload);
            if (!notification) {
                return res.status(404).json({ success: false, message: "Notification not updated. " })
            }
            res.send(new Response({ notification }, "notification updated successfully. ", 200))
        } catch (error) {
            next(error)
        }

    }

    static async delete(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const notificationId = req.params.id;
            const notification = await NotificationService.delete(notificationId);
            if (!notification) {
                return res.status(404).json({ success: false, message: "Notification not deleted. " })
            }
            res.send(new Response({ notification }, "notification deleted successfully. ", 200))
        } catch (error) {
            next(error)
        }
    }

    static async getAll(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const notifications = await NotificationService.getAll();
            res.send(new Response({ notifications }, "notifications fetched successfully. ", 200))
        } catch (error) {
            next(error)
        }
    }

    static async getByTherapistId(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const id = req.params.therapistId.toString();
            const notifications = await NotificationService.getByTherapistId(id);
            res.send(new Response({ notifications }, "notifications fetched successfully. ", 200))
        } catch (error) {
            next(error)
        }
    }

    static async getForTherapist(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const id = req.therapist._id.toString();
            let notifications: any = await NotificationService.getByTherapistId(id);
            if (notifications.length > 0) {
                let requiredNotifications = [];
                for (let notification of notifications) {
                    const updateNotification = await NotificationService.updateNotification(notification._id);
                    requiredNotifications.push(updateNotification)
                }
                notifications = requiredNotifications
            }
            res.send(new Response({ notifications }, "notifications fetched successfully. ", 200))
        } catch (error) {
            next(error)
        }
    }

    static async getTherapistNotifications(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const id = req.therapist._id.toString();
            const notifications: any = await NotificationService.getByTherapistId(id);
            res.send(new Response({ notifications }, "notifications fetched successfully. ", 200))
        } catch (error) {
            next(error)
        }
    }

}