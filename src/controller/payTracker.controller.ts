import express from "express";
import { PayTrackerService } from "../services/payTracker.service";
import PayTrackerModel, {
  CurrencyEnum,
  PaymentTrackerStatusEnum,
  PaymentTrackerTypeEnum,
} from "../models/PayTracker.model";
import moment from "moment";
import { TherapistService } from "../services/therapist.service";
import { ClientService } from "../services/client.service";
import { Mailer } from "../util/mailer";
import { Mailer2 } from "../util/mailer2";
import { sessionCancelledWithoutFeeTemplate } from "../util/emailTemplate/paytrackerEmailTemplate/sessionCancelledWithoutFee";
import { sessionCancelledWithFeeTemplate } from "../util/emailTemplate/paytrackerEmailTemplate/sessionCancelledWithFee";
import { paymentReminderBeforeSessionTemplate } from "../util/emailTemplate/paytrackerEmailTemplate/paymentReminderBeforeSession";
import { paymentReminderAfterSessionTemplate } from "../util/emailTemplate/paytrackerEmailTemplate/paymentReminderAfterSession";
import { secondPaymentReminderTemplate } from "../util/emailTemplate/paytrackerEmailTemplate/secondPaymentReminder";
import { PayTrackerStatusEnum } from "../lib/enum/payTracker.enum";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { baseTemplate } from "../util/emailTemplate/paytrackerEmailTemplate/baseTemplate";
import ScheduleModel, { ScheduleStatus } from "../models/Schedule.model";
import { getIncreaseDecrease } from "../helper/custom.helper";
import { ClientDao } from "../lib/dao/client.dao";
import { PayTrackerDao } from "../lib/dao/payTracker.dao";
import { MailSubjectEnum } from "../lib/enum/subject.enum";
import { format } from "date-fns";
import { sendWhatsAppNotification } from "../services/whatsAppNotification.service";

export class PayTrackerController {
  static async getEnums(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const enums = {
        CurrencyEnum,
        PaymentTrackerStatusEnum,
        PaymentTrackerTypeEnum,
      };
      res.status(200).send({ enums });
    } catch (error) {
      next(error);
    }
  }
  static async getAllPayTracker(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber = Number(req.query.pageNumber) || 1;
      const pageSize = Number(req.query.pageSize) || 8;
      const skip = (pageNumber - 1) * pageSize;

      const allPayTracker = await PayTrackerService.getAllPayTracker(
        pageSize,
        skip
      );
      const total = await PayTrackerService.countPayTracker();
      res.status(200).send({ allPayTracker, total });
    } catch (error) {
      next(error);
    }
  }

  static async getPayTrackerByTherapistId(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber = Number(req.query.pageNumber) || 1;
      const pageSize = Number(req.query.pageSize) || 8;
      const skip = (pageNumber - 1) * pageSize;
      const searchText = req.query.searchText;
      const paidOnTime = req.query.paidOnTime;
      const paidDelayed = req.query.paidDelayed;
      const stillPending = req.query.stillPending;
      const cancelled = req.query.cancelled;
      const startDate = req.query.startDate
        ? String(req.query.startDate)
        : undefined;
      const endDate = req.query.endDate ? String(req.query.endDate) : undefined;
      const sort = req.query.sort == "desc" ? -1 : 1; // -1 for descending (newest first), 1 for ascending (oldest first)
      const clientName = req.query.clientName || undefined;

      const therapistId: any = req.therapist._id;
      if (!therapistId) {
        return res.status(400).send("Therapist id required");
      }

      const query: any = {
        therapistId: therapistId,
        isDeleted: false,
        // "clientId.isActive": true,
      };

      // allPayTracker = allPayTracker.filter(
      //   (item: any) => item.clientId.isActive == true
      // );

      //Status Filter
      const statusFilter = [];
      if (paidOnTime == "true") {
        statusFilter.push(PaymentTrackerStatusEnum.Paid_On_Time);
      }
      if (paidDelayed == "true") {
        statusFilter.push("Paid Delay");
      }
      if (stillPending == "true") {
        statusFilter.push(PaymentTrackerStatusEnum.Still_Pending);
      }
      if (cancelled == "true") {
        // statusFilter.push(PaymentTrackerStatusEnum.Cancelled_Paid_Delayed);
        // statusFilter.push(PaymentTrackerStatusEnum.Cancelled_Paid_On_Time);
        // statusFilter.push(PaymentTrackerStatusEnum.Cancelled_Still_Pending);
        statusFilter.push(PaymentTrackerStatusEnum.Cancelled_Zero_Fee);
        statusFilter.push(PaymentTrackerStatusEnum.Cancelled_Paid_Fee);
      }

      if (statusFilter?.length > 0) {
        query["status"] = { $in: statusFilter };
      }

      // // Search Text Filter
      // // Search Text Filter
      // if (searchText) {
      //   const searchTerms = String(searchText)
      //     .trim()
      //     .split(" ")
      //     .map((term) => term.trim());

      //   // Change the query to search for clientId.name instead of tags
      //   query["$or"] = searchTerms.map((term) => ({
      //     "clientId.name": { $regex: searchText, $options: "i" }, // Use "clientId.name" for the search
      //   }));
      // }

      // Client Name filter
      if (clientName) {
        query["clientId"] = clientName;
      }

      let allPayTracker: any =
        await PayTrackerService.getPayTrackerByTherapistId(query);

      // const total = await PayTrackerService.countPayTrackerByTherapistId(therapistId);

      for (let i = 0; i < allPayTracker?.length; i++) {
        const payTracker = allPayTracker[i];

        for (
          let i = 0;
          i < payTracker?.scheduleId?.recurrenceDates?.length;
          i++
        ) {
          const scheduleRec = payTracker?.scheduleId?.recurrenceDates[i];

          if (
            payTracker?.scheduleRecId?.toString() ==
            scheduleRec?._id?.toString()
          ) {
            // console.log(scheduleRec);
            payTracker.sessionDate = scheduleRec.fromDate;
            payTracker.sessionStatus = scheduleRec.status;
            break;
          }
        }
      }

      let filteredAllPaytrackerData = allPayTracker;

      // Apply the search filter first if searchText is provided
      if (searchText && typeof searchText === "string") {
        const regex = new RegExp(searchText, "i"); // 'i' flag makes it case-insensitive
        filteredAllPaytrackerData = filteredAllPaytrackerData.filter(
          (payTracker: any) => {
            const client = payTracker?.clientId;
            return (
              (client?.name && regex.test(client.name)) || // Check if name matches
              (client?.email && regex.test(client.email)) // Check if email matches
            );
            // return (
            //   client?.isActive === true && // Ensure client is active
            //   ((client?.name && regex.test(client.name)) || // Check if name matches
            //     (client?.email && regex.test(client.email))) // Check if email matches
            // );
          }
        );
      }

      // Apply the date filter if startDate or endDate is provided
      if (startDate || endDate) {
        filteredAllPaytrackerData = filteredAllPaytrackerData.filter(
          (payTracker: any) => {
            const sessionDate = moment(payTracker?.sessionDate).toDate();
            // const sessionDate = moment(payTracker?.sessionDate);
            // console.log("sessionDate", sessionDate);

            const start = startDate ? moment(startDate).toDate() : null;
            const end = endDate ? moment(endDate).toDate() : null;
            // const start = moment(startDate)
            // const end = moment(endDate);

            if (start && end) {
              return sessionDate >= start && sessionDate <= end;
            } else if (start) {
              return sessionDate >= start;
            } else if (end) {
              return sessionDate <= end;
            }
            return true;
          }
        );
      }

      // Sort by sessionDate
      filteredAllPaytrackerData.sort((a: any, b: any) => {
        const dateA = moment(a.sessionDate).toDate();
        const dateB = moment(b.sessionDate).toDate();
        return sort === -1
          ? dateB.getTime() - dateA.getTime()
          : dateA.getTime() - dateB.getTime();
      });

      // Apply pagination after filtering and sorting
      allPayTracker = filteredAllPaytrackerData.filter((item: any) => {
        // If status is "Still pending", don't check isActive, otherwise ensure isActive is true
        // if (item?.status === PaymentTrackerStatusEnum.Still_Pending) {
        //   return true;
        // }

        // For all other statuses, include only if client is active
        // return item?.clientId?.isActive === true;
        return true;
      });
      const total = allPayTracker?.length || 0;
      allPayTracker = allPayTracker.slice(skip, skip + pageSize);
      // const total = filteredAllPaytrackerData?.length || 0;

      res.status(200).send({ allPayTracker, total });
    } catch (error) {
      next(error);
    }
  }

  static async createPayTracker(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId: any = req.therapist._id;
      const payload = req.body;
      payload.therapistId = therapistId;

      const tags = await PayTrackerService.getTagsByScheduleId(
        payload.scheduleId
      );
      tags.push(moment(payload.dueDate).format("DD MMM YYYY"));
      tags.push(payload.status);
      payload.tags = tags;
      const paytracker = await PayTrackerService.createPayTracker(payload);
      if (!paytracker) {
        return res.status(400).send("Unable to create");
      }
      res.status(200).send("Created");
    } catch (error) {
      next(error);
    }
  }

  static async updatePayTracker(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const _id: any = req.params._id;
      const payload: any = req.body;
      const updatedPayTracker = await PayTrackerService.updatePayTracker(
        _id,
        payload
      );
      if (!updatedPayTracker) {
        return res.status(400).send("Paytracker not updated");
      }
      res.status(200).send("Paytracker updated");
    } catch (error) {
      next(error);
    }
  }

  static async deletePayTracker(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const _id: any = req.params._id;
      const deletedPaytracker = await PayTrackerService.deletePayTracker(_id);
      if (!deletedPaytracker) {
        return res.status(400).send("Paytracker not deleted");
      }
      res.status(200).send("Paytracker deleted");
    } catch (error) {
      next(error);
    }
  }

  static async changePayTrackerStatus(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const id = req.params.id;
      const statusValue = req.query.status;

      const payTracker = await PayTrackerModel.findById(id);
      console.log(payTracker?.scheduleRecId);

      const changeStatus = await PayTrackerService.changePayTrackerStatus(
        id,
        statusValue
      );

      if (
        statusValue == "Paid Cancellation" ||
        statusValue == "Free Cancellation"
      ) {
        const schedule = await ScheduleModel.findOneAndUpdate(
          {
            "recurrenceDates._id": payTracker?.scheduleRecId,
          },
          {
            $set: {
              "recurrenceDates.$.status": "cancelled",
            },
          },
          {
            new: true,
          }
        );
      }
      if (!changeStatus) {
        return res.status(404).send("Unable to update");
      }
      res.status(200).send("Status updated");
    } catch (error) {
      next(error);
    }
  }

  static async getStats(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      let startDate: any = req.query.startDate;
      // ? String(req.query.startDate)
      // : undefined;
      let endDate: any = req.query.endDate;
      // ? String(req.query.endDate)
      // : undefined;
      const therapistId = req.therapist._id;

      if (!startDate || !endDate) {
        return res.status(400).send("Start date and end date are required");
      }

      startDate = moment(startDate).toISOString();
      endDate = moment(endDate).toISOString();

      const stats_new = await PayTrackerService.getStatsData(
        therapistId,
        startDate,
        endDate
      );

      // Old data
      const dateDifference = moment(endDate).diff(startDate, "days"); // inclusive of both start and end date

      const changeEndDate = moment(startDate).toISOString();
      const changeStartDate = moment(startDate)
        .subtract(dateDifference, "days")
        .toISOString();

      const stats_old = await PayTrackerService.getStatsData(
        therapistId,
        changeStartDate,
        changeEndDate
      );

      const pending_new = await ClientDao.getPending(
        therapistId,
        startDate,
        endDate
      );
      const pending_old = await ClientDao.getPending(
        therapistId,
        changeStartDate,
        changeEndDate
      );

      let values = {
        // clients: {
        //     data: stats_new.clients,
        //     old: stats_old.clients,
        //     change: getIncreaseDecrease(stats_new.clients, stats_old.clients)
        // },
        // completed_session: {
        //     data: stats_new.completed_session,
        //     old: stats_old.completed_session,
        //     change: getIncreaseDecrease(stats_new.completed_session, stats_old.completed_session)
        // },
        // cancelled_session: {
        //     data: stats_new.cancelled_session,
        //     old: stats_old.cancelled_session,
        //     change: getIncreaseDecrease(stats_new.cancelled_session, stats_old.cancelled_session)
        // },
        collected_payment: {
          data: stats_new?.collected_payment || 0,
          old: stats_old?.collected_payment || 0,
          change: getIncreaseDecrease(
            stats_new?.collected_payment || 0,
            stats_old?.collected_payment || 0
          ),
        },
        pending_payment: {
          data: pending_new || 0,
          old: pending_old || 0,
          change: getIncreaseDecrease(pending_new || 0, pending_old || 0),
        },
        total_earnings: {
          data: stats_new?.total_earnings,
          old: stats_old?.total_earnings,
          change: getIncreaseDecrease(stats_new?.total_earnings || 0, 0),
        },
      };

      res.status(200).send({ values });
    } catch (error) {
      next(error);
    }
  }

  // static async getStats(
  //   req: express.Request,
  //   res: express.Response,
  //   next: express.NextFunction
  // ) {
  //   try {
  //     const startDate = new Date(req.query.startDate as string);
  //     const endDate = new Date(req.query.endDate as string);

  //     if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
  //       return res
  //         .status(400)
  //         .json({ success: false, message: "Invalid startDate or endDate." });
  //     }

  //     const therapistId = req.therapist._id;

  //     // Fetch and calculate totals from PayTrackerModel for 'Paid on time' and 'Paid on delay'
  //     const paidOnTimePayments = await PayTrackerModel.find({
  //       therapistId: therapistId,
  //       status: { $in: ["Paid on time", "Paid delayed"] }, // Include both "Paid on time" and "Paid on delay"
  //       updatedAt: { $gte: startDate, $lte: endDate }, // Filter by payment date range
  //     });

  //     const totalEarnings = paidOnTimePayments.reduce(
  //       (total, payment) => total + Number(payment.amount.value),
  //       0
  //     );

  //     // Fetch and calculate totals for "Still pending" payments
  //     const stillPendingPayments = await PayTrackerModel.find({
  //       therapistId: therapistId,
  //       status: "Still pending",
  //       updatedAt: { $gte: startDate, $lte: endDate }, // Filter by payment date range
  //     });

  //     const stillPendingTotal = stillPendingPayments.reduce(
  //       (total, payment) => total + Number(payment.amount.value),
  //       0
  //     );

  //     // Return response with the calculated data
  //     res.send({
  //       collected_payment: totalEarnings,
  //       pending_payment: stillPendingTotal,
  //       total_earnings: totalEarnings,
  //     });
  //   } catch (error) {
  //     next(error);
  //   }
  // }

  static async getTherapistClientsByName(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const name = String(req.query.name);
      const therapistId = req.therapist._id;

      const allClients = await PayTrackerService.getTherapistClientsByName(
        therapistId,
        name
      );

      const filteredClients = allClients
        .filter((result: any) => result.clientId !== null)
        .map((result: any) => ({
          label: result.clientId.name,
          value: result.clientId._id,
        }));

      res.status(200).send({ filteredClients });
    } catch (error) {
      next(error);
    }
  }

  // static async sendReminder(req: express.Request, res: express.Response, next: express.NextFunction) {
  //     try {
  //         const therapistId = req.therapist._id;
  //         const clientId = req.params.clientId;
  //         const paytrackerId = req.params.paytrackerId;

  //         const therapist  = await TherapistService.getTherapistData(therapistId);
  //         const client  = await ClientService.getClientById(clientId);
  //         const paytracker = await PayTrackerService.getpaytrackerById(paytrackerId);

  //         const paytrackerStatus  = paytracker?.status;
  //         const reminderCount = paytracker?.sendRemainder;

  //         const clientName  = client?.name;
  //         const clientEmail = client?.email;
  //         const therapistName  = therapist?.name;
  //         const therapistEmail = therapist?.email;

  //     } catch (error) {
  //         next(error)
  //     }
  // }

  static async sendReminder(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const clientId = req.params.clientId;
      const paytrackerId = req.params.paytrackerId;
      const templateTest = req.query.templateTest == "true" ? true : false;

      const therapist = await TherapistService.getTherapistData(therapistId);
      const client = await ClientService.getClientById(clientId);
      const paytracker = await PayTrackerService.getpaytrackerById(
        paytrackerId
      );
      if (!paytracker)
        return res.status(400).send("Unable to find paytracker.");

      const schedule = await ScheduleDao.getScheduleByRecurrenceDateId(
        therapistId,
        String(paytracker.scheduleRecId)
      );
      if (!schedule) return res.status(400).send("Unable to find schedule.");

      const recurrenceIdData = schedule.recurrenceDates.find(
        (recurrence: any) =>
          String(recurrence._id) == String(paytracker.scheduleRecId)
      );
      if (!recurrenceIdData)
        return res.status(400).send("Unable to find recurrence date.");

      const paytrackerType = paytracker?.paymentType;

      // const reminderCount = paytracker?.sendRemainder;

      let templateData: any = {
        clientName: client?.name || "There",
        therapistName: therapist?.name || " ",
        sessionDate: moment(recurrenceIdData.fromDate)
          .tz(client?.defaultTimezone || "Asia/Kolkata")
          .format("dddd,D MMM YYYY"),
        sessionTime: moment(recurrenceIdData.fromDate)
          .tz(client?.defaultTimezone || "Asia/Kolkata")
          .format("hh:mm A"),
        amount: paytracker?.amount?.value || 0,
        sessionEndTime: moment(recurrenceIdData.fromDate)
          .tz(client?.defaultTimezone || "Asia/Kolkata")
          .format("hh:mm A"),
      };

      let sessionStart = moment(recurrenceIdData.fromDate).tz(
        client?.defaultTimezone || "Asia/Kolkata"
      );
      let sessionEnd = moment(recurrenceIdData.toDate).tz(
        client?.defaultTimezone || "Asia/Kolkata"
      );
      let durationInMinutes = sessionEnd.diff(sessionStart, "minutes");

      templateData.sessionDurationInMinutes = Number(durationInMinutes);

      let senderData = {
        email: therapist?.email,
        name: therapist?.name,
      };
      const receiverData = [
        {
          email: client?.email,
          name: client?.name || "There",
        },
      ];

      let emailSubject: string = MailSubjectEnum.PAYMENT_REMAINDER;
      let emailBody: string;

      let reminderType = undefined;

      switch (paytracker.status) {
        case PaymentTrackerStatusEnum.Paid_On_Time: {
          return res
            .status(200)
            .send({ message: "Already Paid. Cannot Send Reminder." });
        }

        case PaymentTrackerStatusEnum.Paid_Delayed: {
          return res
            .status(200)
            .send({ message: "Already Paid. Cannot Send Reminder." });

          // paytracker.reminderCounter.delayed = paytracker.reminderCounter.delayed + 1;
          // if(paytracker.reminderCounter.delayed > 0){
          //     reminderType = PayTrackerStatusEnum.SECOND_PAYMENT;
          //     break;
          // }
          // if(paytrackerType == PaymentTrackerTypeEnum.Advance){
          //     reminderType = PayTrackerStatusEnum.FIRST_PAYMENT_BEFORE;
          //     break;
          // }
          // if(paytrackerType == PaymentTrackerTypeEnum.Post_Session){
          //     reminderType = PayTrackerStatusEnum.FIRST_PAYMENT_AFTER;
          //     break;
          // }
        }

        case PaymentTrackerStatusEnum.Still_Pending: {
          if (paytracker.reminderCounter.pending > 0) {
            reminderType = PayTrackerStatusEnum.SECOND_PAYMENT;
            break;
          }
          paytracker.reminderCounter.pending =
            paytracker.reminderCounter.pending + 1;
          // if ( paytrackerType == PaymentTrackerTypeEnum.Advance) {
          //   reminderType = PayTrackerStatusEnum.FIRST_PAYMENT_BEFORE;
          //   break;
          // }

          if (recurrenceIdData.status == ScheduleStatus.COMPLETED) {
            reminderType = PayTrackerStatusEnum.FIRST_PAYMENT_AFTER;
            break;
          } else {
            reminderType = PayTrackerStatusEnum.FIRST_PAYMENT_BEFORE;
            break;
          }
          // if (paytrackerType == PaymentTrackerTypeEnum.Post_Session) {
          //   reminderType = PayTrackerStatusEnum.FIRST_PAYMENT_BEFORE;
          //   break;
          // }
        }
        case PaymentTrackerStatusEnum.Cancelled_Zero_Fee: {
          paytracker.reminderCounter.free_cancel =
            paytracker.reminderCounter.free_cancel + 1;
          reminderType = PayTrackerStatusEnum.CANCELLED_WITHOUT_FEE;
          break;
        }
        case PaymentTrackerStatusEnum.Cancelled_Paid_Fee: {
          paytracker.reminderCounter.paid_cancel =
            paytracker.reminderCounter.paid_cancel + 1;
          reminderType = PayTrackerStatusEnum.CANCELLED_WITH_FEE;
          break;
        }
      }

      switch (reminderType) {
        case PayTrackerStatusEnum.FIRST_PAYMENT_BEFORE: {
          console.log("1");
          emailSubject = `Payment Reminder 🔔`;
          emailBody = paymentReminderBeforeSessionTemplate(templateData);
          break;
        }
        case PayTrackerStatusEnum.FIRST_PAYMENT_AFTER: {
          console.log("2");
          emailSubject = `Payment Reminder 🔔`;
          emailBody = paymentReminderAfterSessionTemplate(templateData);
          break;
        }
        case PayTrackerStatusEnum.SECOND_PAYMENT: {
          console.log("3");
          emailSubject = `Payment Reminder 🔔`;
          emailBody = secondPaymentReminderTemplate(templateData);
          break;
        }
        case PayTrackerStatusEnum.CANCELLED_WITHOUT_FEE: {
          console.log("4");
          emailSubject = ` Payment Reminder 🔔`;
          emailBody = sessionCancelledWithoutFeeTemplate(templateData);
          break;
        }
        case PayTrackerStatusEnum.CANCELLED_WITH_FEE: {
          console.log("5");
          emailSubject = `Payment Reminder 🔔`;
          emailBody = sessionCancelledWithFeeTemplate(templateData);
          break;
        }
        default: {
          return res.status(404).send({ message: "Payment status not found." });
        }
      }

      if (templateTest) {
        res.status(200).send({
          emailBody,
          emailSubject,
          receiverData: { email: client?.email, name: client?.name || "There" },
        });
      } else {
        // const templateHTML = baseTemplate(emailBody);
        await Mailer2.sendMail(
          senderData,
          receiverData,
          emailSubject,
          emailBody
          // templateHTML
        );

        const formattedDate = format(new Date(schedule?.recurrenceDates[0]?.fromDate), "dd-MM-yyyy") 
        const formattedTime = format(new Date(schedule?.recurrenceDates[0]?.fromDate), "HH:mm") 

        const params = {
          CLIENT_NAME: client?.name,
          THERAPIST_NAME: therapist?.name ,
          SESSION_DATE: formattedDate,
          SESSION_TIME: formattedTime,
        };

        await sendWhatsAppNotification(schedule, "payment_reminder1", params);
        await paytracker.save();
        res.status(200).send("Reminder sent successfully.");
      }
    } catch (error) {
      next(error);
    }
  }

  static async cancellationCharge(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const _id = req.params._id;
      const charge = req.body.charge;

      const payTracker = await PayTrackerService.getpaytrackerByTherapistandId(
        _id,
        req.therapist._id
      );
      if (!payTracker) {
        return res.status(404).send("Payment not found");
      }

      payTracker.isFine = true;
      payTracker.cancellationFee.currency = CurrencyEnum.INR;
      payTracker.cancellationFee.value = charge;

      payTracker.status = PaymentTrackerStatusEnum.Cancelled_Paid_Fee;

      await payTracker.save();

      res.status(200).send("Charge applied");
    } catch (e) {
      next(e);
    }
  }

  static async updateAmount(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const payId = req.params.paytracker;
      const amount = req.body.amount;

      const paytracker = await PayTrackerService.getpaytrackerByTherapistandId(
        payId,
        req.therapist._id
      );
      if (!paytracker) {
        return res.status(404).send("Payment not found");
      }
      if (paytracker.status != PaymentTrackerStatusEnum.Still_Pending) {
        return res
          .status(400)
          .send("Cannot update amount for " + paytracker.status);
      }
      paytracker.amount.value = amount;
      await paytracker.save();

      const rec_data = await ScheduleDao.updateRecurrenceAmountForPayTracker(
        req.therapist._id,
        paytracker.scheduleId,
        paytracker.scheduleRecId,
        amount,
        paytracker._id
      );

      if (!rec_data) {
        return res.status(400).send("Unable to update recurrence data");
      }
      res.status(200).send("Updated");
    } catch (e) {
      next(e);
    }
  }
}
