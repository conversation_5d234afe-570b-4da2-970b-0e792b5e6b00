import express from "express";
import { AdminService } from "../services/admin.service";
import { Utility } from "../util/util";
import { Response, throwError } from "../util/response";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import ScheduleModel, { ScheduleStatus } from "../models/Schedule.model";
import { TransactionService } from "../services/transaction.service";
import { TherapistService } from "../services/therapist.service";
import { ScheduleService } from "../services/schedule.service";
import InvoiceService from "../services/invoice.service";
import { DeductionService } from "../services/deductionService";
import { ClientService } from "../services/client.service";
import { PayoutService } from "../services/payout.service";
import InvoiceDao from "../lib/dao/invoice.dao";
import { DeductionDao } from "../lib/dao/deduction.dao";
import moment from "moment";
import { PayoutMode, PayoutStatus } from "../models/payouts.model";
import { PayoutDao } from "../lib/dao/payout.dao";
import RazorpayService from "../services/razorpay.service";
import { paymentMethod, paymentStatus } from "../lib/enum/cashfree.enum";
import fs from "fs";
import { FileUploadService } from "../services/uploadService";
import { CalendarEventDao } from "../lib/dao/calendarEvent.dao";
import { TransactionDao } from "../lib/dao/transaction.dao";
import {
  getBestPaymentMonths,
  getBestSessionMonths,
} from "../helper/custom.helper";
import TherapistSubscriptionService from "../services/therapistsubscription.service";
import ExcelJS from "exceljs";
import { CONFIG } from "../config/environment";
import path from "path";
import { TherapistDao } from "../lib/dao/therapist.dao";
export class AdminController {
  static async login(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      let admin: any = await AdminService.getByEmail(req.body.email);
      if (!admin) {
        return res.status(400).send("No such admin exist");
      }

   
      if (!Utility.comparePasswordHash(admin?.password!, req.body.password)) {
        return res.status(400).send("Incorrect password");
      }

      // Generate JWT token and return token
      let token = Utility.generateJwtToken(admin?._id);
      res.send(new Response({ token }, `token created successfully`, 200));
    } catch (error) {
      next(error);
    }
  }

  static async updateBankDetails(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const bankDetails = req.body;
      const therapistId = req.params.id;
      const updatedData = await AdminService.updateBankDetails(
        therapistId,
        bankDetails
      );
      if (!updatedData) {
        return res.status(404).send("Bank Details Not Upated.");
      }
      res.send(
        new Response({ updatedData }, "Bank details updated successfully.", 200)
      );
    } catch (err) {
      next(err);
    }
  }

  //   static async getAllSessions(
  //     req: express.Request,
  //     res: express.Response,
  //     next: express.NextFunction
  //   ) {
  //     try {
  //       const pageNumber: any = Number(req.query.pageNumber) || 1;
  //       const pageSize: any = Number(req.query.pageSize) || 20;
  //       const skip = (pageNumber - 1) * pageSize;

  //       const therapistId: any = req.query.therapist;

  //       const schedules = await ScheduleDao.getAllRecurrenceDates(therapistId);
  //       let allRecurrenceDates: any = [];
  //       for (let schedule of schedules) {
  //         allRecurrenceDates.push(schedule.recurrenceDates);
  //       }
  //       const reqRecurrenceDates: any = [].concat(...allRecurrenceDates);
  //       const latestRecurrenceDates = reqRecurrenceDates.sort(
  //         (a: any, b: any) => a.toDate - b.toDate
  //       );
  //       let reqData = [];
  //       for (let recurrenceDate of latestRecurrenceDates) {
  //         const schedule: any = await ScheduleModel.findOne({
  //           "recurrenceDates._id": recurrenceDate,
  //         }).populate({
  //           path: "therapistId",
  //           select: "name email _id",
  //         });
  //         const scheduleData = {
  //           _id: schedule?._id,
  //           // "email": schedule?.email,
  //           // "additionalEmails": schedule?.additionalEmails,
  //           scheduleId: schedule?.scheduleId,
  //           // "name": schedule?.name,
  //           fromDate: recurrenceDate?.fromDate,
  //           toDate: recurrenceDate?.toDate,
  //           status: recurrenceDate?.status,
  //           therapistId: schedule?.therapistId,
  //           // "clientCountry": schedule?.clientCountry,
  //           recurrence: schedule?.recurrence,
  //           isActive: schedule?.isActive,
  //           syncWithCalender: schedule?.syncWithCalender,
  //           tillDate: schedule?.tillDate,
  //           description: schedule?.description,
  //           createdAt: schedule?.createdAt,
  //           updatedAt: schedule?.updatedAt,
  //           recurrenceDateId: recurrenceDate._id,
  //           // "phone": schedule?.phone
  //         };
  //         reqData.push(scheduleData);
  //       }
  //       const paginationData = reqData.slice(skip, skip + pageSize);
  //       res.send({ appointments: paginationData });
  //     } catch (error) {
  //       next(error);
  //     }
  //   }

  static async getAllSessions(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber = Number(req.query.pageNumber) || 1;
      const pageSize = Number(req.query.pageSize) || 20;
      const skip = (pageNumber - 1) * pageSize;

      const therapistId = req.query.therapist;

      const schedules = await ScheduleModel.find(
        { therapistId: therapistId },
        {},
        { sort: { "recurrenceDates.toDate": 1 } }
      )
        .populate({
          path: "therapistId",
          select: "name email _id",
        })
        .lean();

      const allSessions = schedules.flatMap((schedule: any) =>
        schedule.recurrenceDates.map((recurrenceDate: any) => ({
          _id: schedule._id,
          scheduleId: schedule.scheduleId,
          fromDate: recurrenceDate.fromDate,
          toDate: recurrenceDate.toDate,
          status: recurrenceDate.status,
          therapistId: {
            _id: schedule.therapistId._id,
            email: schedule.therapistId.email,
            name: schedule.therapistId.name,
          },
          recurrence: schedule.recurrence,
          isActive: schedule.isActive,
          syncWithCalender: schedule.syncWithCalender,
          tillDate: schedule.tillDate,
          description: schedule.description,
          createdAt: schedule.createdAt,
          updatedAt: schedule.updatedAt,
          recurrenceDateId: recurrenceDate._id,
        }))
      );

      const paginateData = allSessions.slice(skip, skip + pageSize);

      return res.status(200).send({ appointments: paginateData });
    } catch (error) {
      console.error(error);
      next(error);
    }
  }

  static async getAllTransactions(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber: any = Number(req.query.pageNumber) || 1;
      const pageSize: any = Number(req.query.pageSize) || 20;
      const skip = (pageNumber - 1) * pageSize;

      const therapistId: any = req.query.therapist;

      const transaction = await TransactionService.getAllTransactions(
        therapistId,
        skip,
        pageSize
      );
      res.send({ transactions: transaction });
    } catch (error) {
      next(error);
    }
  }

  static async updateTherapist(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.params.id;
      let therapistData = req.body;

      const updatedData = await AdminService.updateTherapist(
        therapistId,
        therapistData
      );
      if (!updatedData) {
        return res.status(404).send("Therapist Not Upated.");
      }

      res.send(
        new Response({ updatedData }, "Therapist updated successfully.", 200)
      );
    } catch (error) {
      next(error);
    }
  }

  static async getStats(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapist = await TherapistService.getCount();
      const sessions = await ScheduleService.getCount(30);
      const transactions = await TransactionService.getCount(30);
      const pending = await TransactionService.getPendingCount(30);

      res.send({ therapist, sessions, transactions, pending });
    } catch (error) {
      next(error);
    }
  }

  static async getTherapistStats(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapist = await TherapistService.getCount();
      const therapist_last_30_days = await TherapistService.getCount(30);
      const approved = await TherapistService.getApprovedCount();

      res.send({ therapist, therapist_last_30_days, approved });
    } catch (e) {
      next(e);
    }
  }

  static async createInvoice(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const {
        scheduleId,
        therapistId,
        clientId,
        scheduleRecId,
        isInternational,
      } = req.body;

      const invoice = await InvoiceService.createInvoice(
        therapistId,
        clientId,
        scheduleId,
        scheduleRecId,
        isInternational
      );
      res.send({ invoice });
    } catch (e) {
      next(e);
    }
  }

  static async createDeduction(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const adminId: any = req.admin._id;
      const therapistId: any = req.params.therapistId;
      const { amount, deductionType, deductionDate } = req.body;

      const deduction = await DeductionService.createDeduction(
        adminId,
        therapistId,
        amount,
        deductionType,
        deductionDate
      );
      res.send({ deduction });
    } catch (e) {
      next(e);
    }
  }

  static async getAllDecution(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber: any = Number(req.query.pageNumber) || 1;
      const pageSize: any = Number(req.query.pageSize) || 20;
      const skip = (pageNumber - 1) * pageSize;
      const therapistId: any = req.query.therapistId;
      const isNotPaid: any = req.query.isNotPaid;

      const getDeduciton = await DeductionService.getAllDecution(
        pageSize,
        skip,
        therapistId,
        isNotPaid
      );
      res.send(getDeduciton);
    } catch (e) {
      next(e);
    }
  }

  static async getDeducitonById(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const deductionId = req.params.id;
      const getDeduction = await DeductionService.getDeductionById(deductionId);
      if (!getDeduction) {
        return res.status(404).send("no deduction found. ");
      }
      res.send(getDeduction);
    } catch (e) {
      next(e);
    }
  }

  static async updateDeduction(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const deductionId = req.params.id;
      const { amount, deductionType, deductionDate } = req.body;

      const deduction: any = await DeductionService.updateDeduction(
        deductionId,
        {
          amount,
          deductionType,
          deductionDate,
        }
      );
      if (!deduction) {
        return res
          .status(404)
          .send("no deduction found or deduction transferred");
      }
      res.send({ deduction });
    } catch (e) {
      next(e);
    }
  }

  static async deleteDeduction(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const deductionId = req.params.id;
      const getDeduction = await DeductionService.deleteDeduction(deductionId);
      if (!getDeduction) {
        return res
          .status(404)
          .send("no deduction found or deduction transferred");
      }
      res.send(getDeduction);
    } catch (e) {
      next(e);
    }
  }

  static async getAllClients(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber: any = Number(req.query.pageNumber) || 1;
      const pageSize: any = Number(req.query.pageSize) || 20;
      const skip = (pageNumber - 1) * pageSize;
      const therapistId: any = req.query.therapistId;

      const clients = await ClientService.getAll(pageSize, skip, therapistId);
      if (!clients) {
        return res.status(400).send("Unable to get clients.");
      }
      res.send(clients);
    } catch (e) {
      next(e);
    }
  }

  static async getSchedules(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber: any = Number(req.query.pageNumber) || 1;
      const pageSize: any = Number(req.query.pageSize) || 20;
      const skip = (pageNumber - 1) * pageSize;
      const therapistId: any = req.query.therapistId;
      const clientId: any = req.query.clientId;

      const schedules = await ScheduleService.getAll(
        pageSize,
        skip,
        therapistId,
        clientId
      );
      if (!schedules) {
        return res.status(400).send("Unable to get schedules.");
      }
      res.send(schedules);
    } catch (e) {
      next(e);
    }
  }

  static async getInvoices(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber: any = Number(req.query.pageNumber) || 1;
      const pageSize: any = Number(req.query.pageSize) || 20;
      const skip = (pageNumber - 1) * pageSize;
      const therapistId: any = req.query.therapistId;
      const clientId: any = req.query.clientId;
      const isNotPaid: any = req.query.isNotPaid;
      const fromDate: any =
        req.query.fromDate || moment().subtract(1, "month").toISOString();
      const toDate: any = req.query.toDate || new Date().toISOString();

      const invoices = await InvoiceService.getAll(
        pageSize,
        skip,
        therapistId,
        clientId,
        isNotPaid,
        fromDate,
        toDate
      );

      const invoiceCount = await InvoiceService.getCount(
        therapistId,
        clientId,
        isNotPaid,
        fromDate,
        toDate
      );
      if (!invoices) {
        return res.status(400).send("Unable to get invoices.");
      }
      res.send({ invoices: invoices, totalCount: invoiceCount });
    } catch (e) {
      next(e);
    }
  }

  static async getPayouts(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber: any = Number(req.query.pageNumber) || 1;
      const pageSize: any = Number(req.query.pageSize) || 20;
      const skip = (pageNumber - 1) * pageSize;
      const therapistId: any = req.query.therapistId;
      const status: any = req.query.status;

      const payouts = await PayoutService.getAll(
        pageSize,
        skip,
        therapistId,
        status
      );
      if (!payouts) {
        return res.status(400).send("Unable to get payouts.");
      }
      res.send({ payouts });
    } catch (e) {
      next(e);
    }
  }

  static async getClientTransactions(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber: any = Number(req.query.pageNumber) || 1;
      const pageSize: any = Number(req.query.pageSize) || 20;
      const skip = (pageNumber - 1) * pageSize;
      const therapistId: any = req.query.therapistId;
      const status: any = req.query.status;

      const fromDate: any =
        req.query.fromDate || moment().subtract(1, "month").toISOString();
      const toDate: any = req.query.toDate || new Date().toISOString();

      const clientTransactions = await TransactionService.getAll(
        pageSize,
        skip,
        therapistId,
        status,
        fromDate,
        toDate
      );
      // console.log(clientTransactions, "clientTransactions")
      const transactions = clientTransactions.map((transaction: any) => {
        const schedules: any = transaction.scheduleId;
        const rec: any = schedules?.recurrenceDates?.find(
          (recurrenceDate: any) =>
            String(recurrenceDate._id) == String(transaction.scheduleRecId)
        );
        const fromDate = rec?.fromDate;
        const toDate = rec?.toDate;

        return {
          _id: transaction._id,
          amount: transaction.amount,
          clientId: transaction.clientId,
          therapistId: transaction.therapistId,
          paymentStatus: transaction.paymentStatus,
          paymentMethod: transaction.paymentMethod,
          gatewayCharges: transaction.gatewayCharges,
          paymentLink: transaction.paymentLink,
          fromDate: fromDate,
          toDate: toDate,
          createdAt: transaction.createdAt,
        };
      });
      const clientTransactionsCount = await TransactionService.getAllCount(
        therapistId,
        status,
        fromDate,
        toDate
      );
      if (!clientTransactions) {
        return res.status(400).send("Unable to get clientTransactions.");
      }
      res.send({
        clientTransactions: transactions,
        count: clientTransactionsCount,
      });
    } catch (e) {
      next(e);
    }
  }

  static async createPayout(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId: any = req.params.therapistId;
      let { invoiceIds, deductionIds, commission } = req.body;
      let invoiceAmount = 0;
      let deductionAmount = 0;
      for (let invoiceId of invoiceIds) {
        const invoice = await InvoiceDao.getBy_IdAndTherapist(
          therapistId,
          invoiceId
        );
        if (!invoice) {
          return throwError("No invoice found for " + invoiceId, 500);
        }
        invoiceAmount = invoiceAmount + Number(invoice?.itemTotal || 0);
      }
      for (let deductionId of deductionIds) {
        const deduction = await DeductionDao.getDeductionByIdAndTherapist(
          therapistId,
          deductionId
        );
        if (!deduction) {
          return throwError("No deduction found for " + deductionId, 500);
        }
        deductionAmount = deductionAmount + Number(deduction?.amount || 0);
      }
      const amount = Number(invoiceAmount - deductionAmount);
      const amountToBePaid = amount - (Number(commission) / 100) * amount;

      const payoutData = {
        therapistId,
        invoices: invoiceIds,
        deductions: deductionIds,
        commission: Number(commission),
        totalAmount: amount,
        amountToBePaid: amountToBePaid,
      };
      const payout = await PayoutService.create(payoutData);
      if (!payout) {
        return throwError("Unable to create payout", 500);
      }
      for (let invoiceId of invoiceIds) {
        const invoice = await InvoiceDao.updateInvoice(
          therapistId,
          invoiceId,
          payout._id
        );
        if (!invoice) {
          return throwError("Unable to update invoice " + invoiceId, 500);
        }
      }
      for (let deductionId of deductionIds) {
        const deduction = await DeductionDao.updateDeductionForPayoutId(
          therapistId,
          deductionId,
          payout._id
        );
        if (!deduction) {
          return throwError("Unable to update deduction " + deductionId, 500);
        }
      }

      res.send({ payout });
    } catch (e) {
      next(e);
    }
  }

  static async makePayoutPayment(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const payoutId: any = req.params.id;
      const payout: any = await PayoutService.getPayoutById(payoutId);
      if (!payout) {
        return throwError("No payout found", 500);
      }
      const rzpPayout = await RazorpayService.createPayout(
        payout.therapistId,
        payout
      );
      if (!rzpPayout) {
        return throwError("Payout Failed", 500);
      }
      payout.payoutMode = PayoutMode.RZP_UPI;
      payout.payoutStatus = PayoutStatus.PAID;
      await payout.save();
      res.send({ payout });
    } catch (e) {
      next(e);
    }
  }

  static async getPayoutById(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const payoutId = req.params.id;
      const populated = req.query.populated || undefined;
      const payout = await PayoutService.getPayoutById(payoutId, populated);
      if (!payout) {
        return res.status(404).send("No Payout found. ");
      }
      res.send({ payout });
    } catch (e) {
      next(e);
    }
  }

  static async deletePayout(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const payoutId = req.params.id;
      const payout = await PayoutService.deletePayout(payoutId);
      if (!payout) {
        return res.status(404).send("Unable to delete payout.");
      }
      res.send({ payout });
    } catch (e) {
      next(e);
    }
  }

  static async updatePayout(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const payoutId = req.params.id;
      const { transferDate, payoutStatus, payoutMode, refId } = req.body;

      const payoutData = {
        transferDate: transferDate,
        payoutStatus: payoutStatus,
        payoutMode: payoutMode,
        refId: refId,
      };
      const payout: any = await PayoutService.updatePayout(
        payoutId,
        payoutData
      );
      if (!payout) {
        return res.status(404).send("Unable to update payout.");
      }
      res.send({ payout });
    } catch (e) {
      next(e);
    }
  }

  static async getInvoiceStats(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const timeBefore30Days = moment().subtract(1, "month").toISOString();
      const invoices = await InvoiceService.getInvoicesForLastMonth(
        timeBefore30Days
      );
      const totalInvoices = await InvoiceDao.getAllInvoices();
      res.send({
        totalInvoices: totalInvoices,
        last30DayInvoices: invoices.last30DayInvoices,
        last30DayInvoiceValue: invoices.last30DayInvoiceValue,
      });
    } catch (e) {
      next(e);
    }
  }

  static async getDeductionStats(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const totalDeductions = await DeductionService.getCount(999999);
      const last30DaysDeductions = await DeductionService.getCount(30);
      const deductionAmount30Days = await DeductionService.getDeductionAmount(
        30
      );

      res.send({
        totalDeductions,
        last30DaysDeductions,
        deductionAmount30Days,
      });
    } catch (e) {
      next(e);
    }
  }

  static async getPayoutStats(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const last30DaysPayouts = await PayoutService.getCount(30);
      const payoutAmount30Days = await PayoutService.getPayoutAmount(30);
      const pendingPayouts = await PayoutService.getStatusCount(
        999999,
        PayoutStatus.PENDING
      );
      const paidPayouts = await PayoutService.getStatusCount(
        999999,
        PayoutStatus.PAID
      );
      res.send({
        last30DaysPayouts,
        payoutAmount30Days,
        pendingPayouts,
        paidPayouts,
      });
    } catch (e) {
      next(e);
    }
  }

  static async deleteInvoice(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const invoiceId = req.params.id;
      const invoice = await InvoiceService.getInvoiceById(invoiceId);
      if (invoice?.payoutId) {
        return res.status(500).send("Payout Id is present.");
      }
      const deleteInvoice = await InvoiceDao.deleteInvoiceById(invoiceId);
      if (!deleteInvoice) {
        return res.status(500).send("Unable to delete invoice");
      }
      res.send({ deleteInvoice });
    } catch (e) {
      next(e);
    }
  }

  static async getPendingPayouts(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pendingPayouts: any = await PayoutDao.getPendingPayouts();
      if (!pendingPayouts || pendingPayouts.length == 0) {
        return res.status(500).send("No pending payouts.");
      }
      let csvData = [];
      for (let pendingPayout of pendingPayouts) {
        await csvData.push({
          "Therapist Name": pendingPayout?.therapistId?.name,
          "Therapist UPI Id": pendingPayout?.therapistId?.bankDetails?.upiId,
          "Therapist Phone": pendingPayout?.therapistId?.phone,
          "Reference Id": pendingPayout?.refId,
          Amount: pendingPayout?.amountToBePaid,
        });
      }
      res.send({ csvData });
    } catch (e) {
      next(e);
    }
  }

  static async manuallyVerifyPayment(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const rzp_id = req.params.transactionId;

      const transaction = await TransactionService.getTransactionBy_id(rzp_id);
      if (!transaction) {
        console.log("No Transaction Found with _id: ", rzp_id);
        return res.status(404).send({
          message: "No Transaction Found",
        });
      }

      if (transaction.paymentStatus == paymentStatus.COMPLETE) {
        return res.status(200).send("Payment is already Complete");
      }

      // GET FROM RZP
      const razorpay_payload: any = await RazorpayService.getPaymentLinkDetails(
        transaction.paymentSessionId
      );
      if (!razorpay_payload) {
        return res
          .status(500)
          .send("Unable to request RZP check if payment link exists");
      }

      if (razorpay_payload.status != "paid") {
        return res.status(500).send("Payment is not paid.");
      }

      if (razorpay_payload.amount_paid != razorpay_payload.amount) {
        return res
          .status(500)
          .send("Amount paid is not equal to total amount.");
      }

      const schedule = await ScheduleDao.getByRecurrenceDateId(
        transaction.scheduleRecId
      );
      if (!schedule) {
        console.log("No Schedule Found with transactionId: ", transaction._id);
        return res.status(200).send({
          message: "No Schedule Found",
        });
      }

      transaction.paymentDetails = razorpay_payload;
      transaction.amountReceived = razorpay_payload.amount_paid / 100;
      transaction.paymentDate = razorpay_payload.created_at;
      transaction.paymentStatus = paymentStatus.COMPLETE;
      transaction.paymentMethod = paymentMethod.RAZORPAY;
      transaction.gatewayCharges = {
        gatewayFee: razorpay_payload?.payload?.payment?.entity?.fee
          ? razorpay_payload?.payload?.payment?.entity?.fee / 100
          : 0,
        gatewayTax: razorpay_payload?.payload?.payment?.entity?.tax
          ? razorpay_payload?.payload?.payment?.entity?.tax / 100
          : 0,
      };

      const clientCountry = schedule.clientCountry === "India" ? false : true;

      const amount =
        transaction.amountReceived -
        (transaction.gatewayCharges.gatewayFee +
          transaction.gatewayCharges.gatewayTax);

      const actualValue = transaction.amountReceived;

      const invoice = await InvoiceService.createInvoice(
        schedule?.therapistId,
        schedule?.clientId,
        schedule._id,
        transaction.scheduleRecId,
        clientCountry,
        amount,
        actualValue
      );
      if (!invoice) {
        console.log("Invoice not created for transactionId: ", transaction._id);
        return res.status(200).send({
          message: "Invoice not created",
        });
      }
      invoice.transactionId = transaction._id;
      invoice.gatewayCharges = {
        gatewayFee: transaction.gatewayCharges.gatewayFee,
        gatewayTax: transaction.gatewayCharges.gatewayTax,
      };
      await transaction.save();
      await invoice.save();
      await schedule.save();
      return res.send({ razorpay_payload });
    } catch (err) {
      next(err);
    }
  }

  static async makeExports(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.body.therapistId;
      const startDate = moment(req.body.startDate, "DD-MM-YYYY")
        .startOf("day")
        .toISOString();
      const endDate = moment(req.body.endDate, "DD-MM-YYYY")
        .endOf("day")
        .toISOString();
      const type = req.body.type;

      let export_data: any[] = [];

      switch (type) {
        case "invoices": {
          const invoices: any = await InvoiceService.getInvoicesForExport(
            therapistId,
            startDate,
            endDate
          );
          const deductions: any = await DeductionService.getDeductionsForExport(
            therapistId,
            startDate,
            endDate
          );
          for (let invoice of invoices) {
            export_data.push({
              Type: "INVOICE",
              "Invoice/Deduction Id": invoice.invoiceSerialNumber,
              "Is Cancelled": invoice.isCancelInvoice ? "Yes" : "No",
              Description: invoice.description,
              "Client Id": invoice.clientId?.clientId,
              Total: invoice.itemTotal,
              Commission: invoice.commission,
              Discount: invoice.discount,
              Amount: invoice.invoiceValue,
              "Actual Amount": invoice.actualValue,
              "Invoice/Deduction Date": moment(invoice.createdAt).format(
                "DD-MM-YYYY hh:mm A"
              ),
              "Payout Id/RZP Id(for deduction)": invoice.payoutId
                ? invoice.payoutId
                : "Not Paid",
            });
          }
          for (let deduction of deductions) {
            export_data.push({
              Type: "DEDUCTION",
              "Invoice/Deduction Id": deduction._id,
              "Is Cancelled": "Not Applicable",
              Description: deduction.remarks,
              "Client Id": deduction.clientId?.clientId,
              Total: deduction.amount,
              Commission: 0,
              Discount: 0,
              Amount: deduction.amount,
              "Invoice/Deduction Date": moment(deduction.deductionDate).format(
                "DD-MM-YYYY hh:mm A"
              ),
              "Payout Id/RZP Id(for deduction)": deduction.rzp_deductionId
                ? deduction.rzp_deductionId
                : "Not Paid",
            });
          }
          break;
        }
      }

      res.send({ export_data });
    } catch (e) {
      next(e);
    }
  }

  static async updateTherapistDoc(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.body.therapistId;

      const therapist = await TherapistService.getTherapist(therapistId);
      if (!therapist) {
        return res.status(404).send("No Therapist found.");
      }
      const index = Number(req.body.index) + 1;

      if (!req.file) return res.status(404).send("No File uploaded");
      let filesData: any = req.file;
      const blob = fs.readFileSync(filesData.path);
      const fileName = String(therapistId) + "_doc_" + String(index);

      const fileUpload = await FileUploadService.uploadFile(blob, fileName);
      if (!fileUpload) {
        return res.status(500).send("There was some problem Uploading!");
      }
      res.send("File updated successfully.");
    } catch (error) {
      next(error);
    }
  }

  static async uploadTherapistDocument(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      if (!req.files?.length) return res.status(404).send("No File uploaded");
      let filesData: any = req.files;

      let allDocIds: any = [];
      let docCount = 0;
      for (let file of filesData) {
        const blob = fs.readFileSync(file.path);
        docCount = docCount + 1;

        const fileUpload = await FileUploadService.uploadFile(
          blob,
          file.filename
        );
        if (!fileUpload)
          return res.status(500).send("There was some problem Uploading!");

        allDocIds.push(fileUpload.Location);
      }
      res.send(allDocIds[0]);
    } catch (error) {
      next(error);
    }
  }

  static async getTherapistDetails(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.params.id;
      const toDate: any = req.query.toDate;
      const fromDate: any = req.query.fromDate;
      let totalCollection = 0;

      if (!toDate || !fromDate) {
        return res.status(404).send("Date is required");
      }

      let date: any = {};
      if (req.query.toDate) {
        date = {
          to: moment(toDate, "YYYY-MM-DD").endOf("day").toISOString(),
          from: moment(fromDate, "YYYY-MM-DD").startOf("day").toISOString(),
        };
      }

      const schedules = await ScheduleDao.getAllRecurrenceDates(therapistId);
      let allRecurrenceDates: any = [];
      for (let schedule of schedules) {
        allRecurrenceDates.push(schedule.recurrenceDates);
      }
      const reqRecurrenceDates: any = [].concat(...allRecurrenceDates);

      const allSessions = reqRecurrenceDates.filter(
        (rec: any) =>
          moment(rec.toDate) >= moment(date.from) &&
          moment(rec.toDate) <= moment(date.to)
      ).length;
      const scheduledSessions = reqRecurrenceDates.filter(
        (rec: any) =>
          moment(rec.toDate) >= moment(date.from) &&
          moment(rec.toDate) <= moment(date.to) &&
          rec.status === ScheduleStatus.CONFIRMED
      ).length;
      const cancelledSessions = reqRecurrenceDates.filter(
        (rec: any) =>
          moment(rec.toDate) >= moment(date.from) &&
          moment(rec.toDate) <= moment(date.to) &&
          rec.status === ScheduleStatus.CANCELLED
      ).length;
      const rescheduledSessions = reqRecurrenceDates.filter(
        (rec: any) =>
          moment(rec.toDate) >= moment(date.from) &&
          moment(rec.toDate) <= moment(date.to) &&
          rec.status === ScheduleStatus.RESCHEDULED
      ).length;
      const googleSyncedSessions =
        await CalendarEventDao.findByDateRangeAndTherapistId(
          therapistId,
          date.from,
          date.to
        );

      const clientIds = await ClientService.getClientIdsByTherapist(
        therapistId
      );

      const totalPayments = await TransactionDao.getAllTransactionsByTherapist(
        therapistId
      );
      const completedPayments = await TransactionDao.getCompletedWithDate(
        therapistId,
        date.from,
        date.to
      );
      totalCollection = completedPayments.reduce(
        (total, current) => total + Number(current.amount),
        0
      );
      const totalCompletedPayment = completedPayments.length;
      const totalCancelledPayments = await TransactionDao.getCancelledWithDate(
        therapistId,
        date.from,
        date.to
      );
      const totalPendingPayments = await TransactionDao.getPendingWithDate(
        therapistId,
        date.from,
        date.to
      );
      const totalOfflinePayments = await TransactionDao.getOfflineWithDate(
        therapistId,
        date.from,
        date.to
      );
      const totalFailedPayments = await TransactionDao.getFailedWithDate(
        therapistId,
        date.from,
        date.to
      );

      const sessionMonthData = getBestSessionMonths(reqRecurrenceDates);
      const paymentMonthData = getBestPaymentMonths(completedPayments);

      res.send({
        allSessions,
        scheduledSessions,
        cancelledSessions,
        rescheduledSessions,
        googleSyncedSessions,
        totalPayments,
        totalCancelledPayments,
        totalCompletedPayment,
        totalPendingPayments,
        totalOfflinePayments,
        totalFailedPayments,
        clientIds,
        totalCollection,
        sessionMonthData,
        paymentMonthData,
      });
    } catch (error) {
      next(error);
    }
  }

  static async getDashboardDetails(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const toDate: any = req.query.toDate;
      const fromDate: any = req.query.fromDate;
      let totalCollection = 0;

      if (!toDate || !fromDate) {
        return res.status(404).send("Date is required");
      }

      let date: any = {};
      if (req.query.toDate) {
        date = {
          to: moment(toDate, "YYYY-MM-DD").endOf("day").toISOString(),
          from: moment(fromDate, "YYYY-MM-DD").startOf("day").toISOString(),
        };
      }

      const schedules = await ScheduleDao.getAllRecurrenceDates();
      let allRecurrenceDates: any = [];
      for (let schedule of schedules) {
        allRecurrenceDates.push(schedule.recurrenceDates);
      }
      const reqRecurrenceDates: any = [].concat(...allRecurrenceDates);

      const googleSyncedSessions = await CalendarEventDao.findByDateRange(
        date.from,
        date.to
      );
      const allSessions = reqRecurrenceDates.filter(
        (rec: any) =>
          moment(rec.toDate) >= moment(date.from) &&
          moment(rec.toDate) <= moment(date.to)
      ).length;

      const completedPayments = await TransactionDao.findCompletedWithDateRange(
        date.from,
        date.to
      );
      totalCollection = completedPayments.reduce(
        (total, current) => total + Number(current.amount),
        0
      );

      res.send({
        allSessions,
        googleSyncedSessions,
        totalCollection,
      });
    } catch (error) {
      next(error);
    }
  }

  static async getTherapistPendingTransactions(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.params.therapistId;
      const transactions =
        await TransactionService.getTherapistTransactionByStatus(
          therapistId,
          paymentStatus.PENDING
        );

      const transaction_data = transactions.map((transaction: any) => {
        const recc: any = transaction.scheduleRecId;
        const schedule = transaction.scheduleId;
        const schedule_data = schedule?.recurrenceDates?.find(
          (rec: any) => String(rec._id) == String(recc)
        );
        return {
          _id: transaction._id,
          amount: transaction.amount,
          clientId: transaction.clientId,
          therapistId: transaction.therapistId,
          paymentStatus: transaction.paymentStatus,
          paymentMethod: transaction.paymentMethod,
          schedule: {
            fromDate: schedule_data?.fromDate,
            toDate: schedule_data?.toDate,
            status: schedule_data?.status,
            meetLink: schedule_data?.meetLink,
          },
        };
      });

      res.send({ transactions: transaction_data });
    } catch (e) {
      next(e);
    }
  }

  static async deletePendingTransaction(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const transationId = req.params.transactionId;
      const transaction = await TransactionService.getTransactionBy_id(
        transationId
      );

      if (!transaction) {
        return res.status(400).send("No transaction found.");
      }

      if (transaction.paymentStatus != paymentStatus.PENDING) {
        return res
          .status(400)
          .send("Transaction payment status is not pending");
      }

      if (!transaction.paymentSessionId || !transaction.paymentLink) {
        return res.status(400).send("Payment Link not created.");
      }

      const deletePaymentLink = await RazorpayService.cancelPaymentLink(
        transaction.paymentSessionId
      );
      if (!deletePaymentLink) {
        return res.status(400).send("Unable to cancel payment link.");
      }

      transaction.paymentStatus = paymentStatus.CANCELLED;
      transaction.cancelledBy = req.admin._id;
      await transaction.save();

      res.send("Payment link deleted successfully.");
    } catch (e) {
      next(e);
    }
  }

  static async exportTransactionCSV(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.params.therapistId;
      const therapist = await TherapistService.getTherapist(therapistId);
      if (!therapist) {
        return res.status(404).send("No therapist found.");
      }

      const subscription: any =
        await TherapistSubscriptionService.getPaidByTherapistId(therapistId);
      const transactions: any =
        await TransactionService.getAllPopulatedTransactionsofTherapist(
          therapist
        );
      const payouts = await PayoutService.getTherapistPayouts(
        therapist,
        PayoutStatus.PAID
      );

      const therapistRecords = [
        {
          therapistId: therapist.identifier,
          therapistName: therapist.name,
          subscriptionStartDate:
            subscription.length > 0
              ? moment(subscription[subscription.length - 1].createdAt).format(
                  "YYYY-MM-DD"
                )
              : "--",
          firstPaymentLinkDate:
            transactions.length > 0
              ? moment(transactions[0].createdAt).format("YYYY-MM-DD")
              : "--",
          totalPaymentsCollected: transactions.filter(
            (transaction: any) =>
              transaction.paymentStatus == paymentStatus.COMPLETE
          ).length,
          totalAmountCredited: payouts.reduce(
            (total: number, current: any) => total + current.amountToBePaid,
            0
          ),
        },
      ];

      let clientRecords: any[] = [];
      for (const transaction of transactions) {
        const transaction_det: any = transaction;
        const client: any = transaction.clientId;
        const schedule: any = transaction.scheduleId;

        const recurrence = schedule?.recurrenceDates?.find(
          (rec: any) => String(rec._id) === String(transaction.scheduleRecId)
        );
        const finalAmountAfterGatewayCharges =
          transaction.amountReceived -
          (transaction.gatewayCharges.gatewayFee +
            transaction.gatewayCharges.gatewayTax);
        clientRecords.push({
          clientId: client?.clientId || "--",
          clientEmail: client?.email || "--",
          sessionDate: recurrence?.toDate
            ? moment(recurrence?.toDate).format("YYYY-MM-DD")
            : "--",
          sessionAmount: transaction.amount || "--",
          sessionCredited: finalAmountAfterGatewayCharges || "--",
          paymentLinkCreationDate: transaction_det.createdAt
            ? moment(transaction_det.createdAt).format("YYYY-MM-DD hh:mm a")
            : "--",
          paymentDate: transaction_det?.paymentDetails?.payload?.payment?.entity
            ?.created_at
            ? moment
                .unix(
                  transaction_det?.paymentDetails?.payload?.payment?.entity
                    ?.created_at
                )
                .format("YYYY-MM-DD hh:mm a")
            : "--",
          status: transaction.paymentStatus || "--",
          paymentMethod: transaction_det?.paymentDetails?.payload?.payment
            ?.entity?.method
            ? transaction_det?.paymentDetails?.payload?.payment?.entity?.method
            : "--",
          paymentLink: transaction.paymentLink || "--",
          cancellationFee: transaction.isFine ? "Yes" : "No",
        });
      }

      const workbook = new ExcelJS.Workbook();

      // Create a worksheet for therapist information
      const therapistSheet = workbook.addWorksheet("Therapist Info");
      therapistSheet.columns = [
        { header: "Therapist ID", key: "therapistId", width: 15 },
        { header: "Therapist Name", key: "therapistName", width: 25 },
        {
          header: "Subscription Start date",
          key: "subscriptionStartDate",
          width: 20,
        },
        {
          header: "Date first payment link was created",
          key: "firstPaymentLinkDate",
          width: 25,
        },
        {
          header: "Total payments collected (N)",
          key: "totalPaymentsCollected",
          width: 20,
        },
        {
          header: "Total amount credited so far",
          key: "totalAmountCredited",
          width: 20,
        },
      ];
      therapistSheet.addRows(therapistRecords);

      // Create a worksheet for client information
      const clientSheet = workbook.addWorksheet("Client Info");
      clientSheet.columns = [
        { header: "Client ID", key: "clientId", width: 15 },
        { header: "Client email", key: "clientEmail", width: 25 },
        { header: "Date of session", key: "sessionDate", width: 20 },
        { header: "Session Amount", key: "sessionAmount", width: 15 },
        {
          header: "Session credited (after Rzp charges)",
          key: "sessionCredited",
          width: 30,
        },
        {
          header: "Date of payment link creation",
          key: "paymentLinkCreationDate",
          width: 25,
        },
        {
          header: "Date payment was made by client",
          key: "paymentDate",
          width: 25,
        },
        { header: "Status (paid/pending/cancelled)", key: "status", width: 20 },
        {
          header: "Method of payment by client (credit card, UPI etc.)",
          key: "paymentMethod",
          width: 25,
        },
        { header: "Payment link", key: "paymentLink", width: 25 },
        { header: "Is Cancellation Fee", key: "cancellationFee", width: 25 },
      ];
      clientSheet.addRows(clientRecords);

      // Write the workbook to a file
      const savePath = path.join(
        CONFIG.cacheFolderPath,
        `Therapist_${therapistId}_Transaction_Records.xlsx`
      );
      await workbook.xlsx.writeFile(savePath);
      const link = await FileUploadService.uploadFile(
        fs.readFileSync(savePath),
        `Therapist_${therapistId}_Transaction_Records.xlsx`
      );
      res.send({ link: link?.Location });
    } catch (e) {
      next(e);
    }
  }

  static async toggleTherpaistPaymentOption(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.query.therapistId;
      const togglePaymentTracker = req.query.togglePaymentTracker;
      const togglePaymentGateway = req.query.togglePaymentGateway;

      const therapistData = await TherapistDao.getTherapist(therapistId);
      if (!therapistData) {
        return res.status(404).send("NO Therapist found.");
      }
      let isUpdated = false;

      if (togglePaymentTracker) {
        therapistData.menus.paymentTracker =
          !therapistData.menus.paymentTracker;
        isUpdated = true;
      }

      if (togglePaymentGateway) {
        therapistData.menus.paymentGateway =
          !therapistData.menus.paymentGateway;
        isUpdated = true;
      }

      if (isUpdated) {
        await therapistData.save();
      }

      res.send("Updated successfully.");
    } catch (e) {
      next(e);
    }
  }
}
