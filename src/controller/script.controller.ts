import express from "express";
import { Response } from "../util/response";
import { ScriptService } from "../services/script.service";
import { TherapistDao } from "../lib/dao/therapist.dao";
import TherapistModel from "../models/Therapist.model";

export class ScriptController {
  static async AddCalendarEventIdAndMeetLinkIntoOnlineSchedule(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    console.log("***START");

    try {
      const { therapistId } = req?.params;

      // const therapists = await TherapistModel.find({});
      // console.log("therapists ", therapists.length);
      // for (let i = 0; i < therapists.length; i++) {
      //   const therapist = therapists[i];
      //   console.log(`therapist[i]`,therapist?._id);
        
      //   const subscription =
      //     await ScriptService.AddCalendarEventIdAndMeetLinkIntoOnlineSchedule(
      //       therapist._id
      //     );
      // }

      const subscription =
        await ScriptService.AddCalendarEventIdAndMeetLinkIntoOnlineSchedule(
          therapistId
        );

      res.status(200).send({
        message: "Data updated successfully.",
      });
    } catch (error) {
      console.log(error);

      next(error);
    }
  }
}
