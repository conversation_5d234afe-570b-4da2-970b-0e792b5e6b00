import express from "express";
import { paymentReminderAfterSessionTemplate } from "../util/emailTemplate/paytrackerEmailTemplate/paymentReminderAfterSession";
import { PaytrackerEmailSubjectEnum } from "../lib/enum/emailEnum/payTrackerEmailEnum";
import { Mailer2 } from "../util/mailer2";

export class TestController {
  static async PaytrackerSendmailTest(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const payload: any = {
        clientName: "test",
        therapistName: "Test Therapist",
        sessionDate: "09/07/2024",
        sessionTime: "5:12pm",
      };
      const htmlTemplate = paymentReminderAfterSessionTemplate(payload);
      const subject =
        PaytrackerEmailSubjectEnum.FIRST_PAYMENT_REMINDER_AFTER_SESSION;

      const senderData = {
        name: "test",
        email: "<EMAIL>",
      };

      const receiverData = [
        {
          name: payload.clientName || "There",
          email: "<EMAIL>",
        },
      ];

      const email = await Mailer2.sendMail(
        senderData,
        receiverData,
        subject,
        htmlTemplate
      );
      if (!email) {
        res.status(404).send("Email not sent");
      }
      res.status(200).send({
        message: "ok",
        data: email,
      });
    } catch (error) {
      next(error);
    }
  }
}
