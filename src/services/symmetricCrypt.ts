import crypto from "crypto";
import { SymmetricCrypt } from "../util/symmetricCrypt";


export class SymmetricCryptService {
    static async createRandomKey() {
        const key = (crypto.randomBytes(32)).toString('hex').slice(0,32);
        return key;
    }

    static async encryptData(data: string, key: string){
        const encryptedData = await SymmetricCrypt.generateCipherData(data,key); 
        return encryptedData;
    }

    static async decryptData(data: string, key: string){
        const decryptedData = await SymmetricCrypt.decipherData(data,key);
        return decryptedData;
    }
}