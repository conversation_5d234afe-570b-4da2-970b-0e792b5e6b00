import { SubscriptionTransactionDao } from "../lib/dao/subscriptionTransaction.dao";

export class SubscriptionTransactionService{
    static async create(data: any){
        return await SubscriptionTransactionDao.create(data)
    }

    static async getByRzpOrder(orderId: string){
        return await SubscriptionTransactionDao.getByRzpOrder(orderId)
    }

    static async getTransactionSubscriptionPopulated(id:any){
        return await SubscriptionTransactionDao.getTransactionSubscriptionPopulated(id);
    }

    static async getByTherapistId(id:any){
        return await SubscriptionTransactionDao.getByTherapistId(id);
    }

    static async getByTherapistIdAll(id:any){
        return await SubscriptionTransactionDao.getByTherapistIdAll(id);
    }

    static async getActiveSubscriptionCount() {
        return await SubscriptionTransactionDao.getActiveSubscriptionCount();
    }
}