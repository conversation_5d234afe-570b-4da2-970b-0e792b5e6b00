/**
 * Automatic Calendar Sync Service
 * 
 * This service provides reliable calendar synchronization by:
 * 1. Running periodic manual syncs for all active therapists
 * 2. Detecting and handling series rescheduling automatically
 * 3. Providing fallback when webhooks fail
 */

import { OptimizedCalendarSyncService } from './optimizedCalendarSync.service';
import { TherapistDao } from '../lib/dao/therapist.dao';
import { GoogleCalendarService } from './googleCalendar.service';

export class AutomaticCalendarSyncService {
  private static syncInterval: NodeJS.Timeout | null = null;
  private static isRunning = false;

  /**
   * Start automatic sync for all therapists
   */
  static startAutomaticSync(intervalMinutes: number = 3): void {
    if (this.isRunning) {
      console.log('⚠️ Automatic sync is already running');
      return;
    }

    console.log(`🚀 Starting automatic calendar sync every ${intervalMinutes} minutes`);
    
    this.isRunning = true;
    this.syncInterval = setInterval(async () => {
      await this.syncAllActiveTherapists();
    }, intervalMinutes * 60 * 1000);

    // Run initial sync
    this.syncAllActiveTherapists();
  }

  /**
   * Stop automatic sync
   */
  static stopAutomaticSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      this.isRunning = false;
      console.log('🛑 Automatic calendar sync stopped');
    }
  }

  /**
   * Sync all active therapists
   */
  private static async syncAllActiveTherapists(): Promise<void> {
    try {
      console.log(`🔄 Running automatic sync at ${new Date().toISOString()}`);

      // Get all therapists with Google Calendar connected
      const therapists = await this.getActiveTherapists();
      
      if (therapists.length === 0) {
        console.log('📭 No active therapists found for sync');
        return;
      }

      console.log(`👥 Found ${therapists.length} active therapists to sync`);

      let totalSynced = 0;
      let totalUpdated = 0;
      let totalCreated = 0;
      let totalErrors = 0;

      // Sync each therapist
      for (const therapist of therapists) {
        try {
          const syncResult = await OptimizedCalendarSyncService.performIncrementalSync(
            therapist._id,
            {
              incremental: true,
              maxResults: 100
            }
          );

          if (syncResult.success) {
            totalSynced += syncResult.syncedEvents || 0;
            totalUpdated += syncResult.updatedEvents || 0;
            totalCreated += syncResult.createdEvents || 0;

            // Log significant changes
            if (syncResult.updatedEvents > 0 || syncResult.createdEvents > 0) {
              console.log(`✅ Therapist ${therapist._id}: ${syncResult.updatedEvents} updated, ${syncResult.createdEvents} created`);
            }
          } else {
            totalErrors++;
            console.error(`❌ Sync failed for therapist ${therapist._id}:`, syncResult.errors);
          }
        } catch (error: any) {
          totalErrors++;
          console.error(`💥 Error syncing therapist ${therapist._id}:`, error.message);
        }
      }

      // Summary log
      if (totalUpdated > 0 || totalCreated > 0 || totalErrors > 0) {
        console.log(`📊 Automatic sync summary: ${totalUpdated} updated, ${totalCreated} created, ${totalErrors} errors`);
      }

    } catch (error: any) {
      console.error('💥 Error in automatic sync:', error.message);
    }
  }

  /**
   * Get all therapists with active Google Calendar connections
   */
  private static async getActiveTherapists(): Promise<any[]> {
    try {
      // This is a simplified approach - you might need to adjust based on your data structure
      const allTherapists = await TherapistDao.getAllTherapists();
      const activeTherapists = [];

      for (const therapist of allTherapists) {
        try {
          const googleCalendarData = await GoogleCalendarService.findByTherapist(therapist._id);
          if (googleCalendarData && googleCalendarData.access_token) {
            activeTherapists.push(therapist);
          }
        } catch (error) {
          // Skip therapists without Google Calendar
          continue;
        }
      }

      return activeTherapists;
    } catch (error: any) {
      console.error('Error getting active therapists:', error.message);
      return [];
    }
  }

  /**
   * Manual trigger for immediate sync
   */
  static async triggerImmediateSync(): Promise<{
    success: boolean;
    therapistsSynced: number;
    totalUpdated: number;
    totalCreated: number;
    errors: string[];
  }> {
    console.log('🚀 Triggering immediate sync for all therapists');
    
    const result = {
      success: true,
      therapistsSynced: 0,
      totalUpdated: 0,
      totalCreated: 0,
      errors: [] as string[]
    };

    try {
      const therapists = await this.getActiveTherapists();
      
      for (const therapist of therapists) {
        try {
          const syncResult = await OptimizedCalendarSyncService.performIncrementalSync(
            therapist._id,
            {
              incremental: true,
              maxResults: 100
            }
          );

          if (syncResult.success) {
            result.therapistsSynced++;
            result.totalUpdated += syncResult.updatedEvents || 0;
            result.totalCreated += syncResult.createdEvents || 0;
          } else {
            result.errors.push(`Therapist ${therapist._id}: ${syncResult.errors.join(', ')}`);
          }
        } catch (error: any) {
          result.errors.push(`Therapist ${therapist._id}: ${error.message}`);
        }
      }

      if (result.errors.length > 0) {
        result.success = false;
      }

      return result;
    } catch (error: any) {
      result.success = false;
      result.errors.push(error.message);
      return result;
    }
  }

  /**
   * Get sync status
   */
  static getSyncStatus(): {
    isRunning: boolean;
    intervalMinutes: number | null;
    lastSyncTime: string | null;
  } {
    return {
      isRunning: this.isRunning,
      intervalMinutes: this.syncInterval ? 3 : null, // Default interval
      lastSyncTime: null // You could track this if needed
    };
  }
}
