// /**
//  * This file contains functions to detect rescheduled sessions in Google Calendar.
//  * It helps identify when a therapist has rescheduled a session from Google Calendar
//  * rather than using the dashboard.
//  */

// import { CalendarEventDao } from "../lib/dao/calendarEvent.dao";
// import moment from "moment";

// /**
//  * Detects if an event from Google Calendar is a rescheduled session
//  *
//  * @param therapistId The therapist ID
//  * @param googleEvents Events from Google Calendar
//  * @returns An object containing arrays of new and rescheduled events
//  */
// export async function detectRescheduledEvents(
//   therapistId: string,
//   googleEvents: any[]
// ) {
//   // Get all existing events for this therapist
//   const existingEvents = await CalendarEventDao.getTherapistCalendarEvents(therapistId);

//   // Arrays to store results
//   const newEvents: any[] = [];
//   const rescheduledEvents: any[] = [];

//   // Process each Google Calendar event
//   for (const googleEvent of googleEvents) {
//     // Skip events without attendees
//     if (!googleEvent.attendees || googleEvent.attendees.length === 0) {
//       newEvents.push(googleEvent);
//       continue;
//     }

//     // Get attendee emails from the Google event
//     const googleAttendeeEmails = googleEvent.attendees
//       .map((a: any) => a.email)
//       .sort()
//       .join(',');

//     // Check if this event already exists in our database
//     const existingEvent = existingEvents.find(e => e.id === googleEvent.id);
//     if (existingEvent) {
//       // If the event exists and has the same start/end times, it's not rescheduled
//       const googleStartTime = new Date(googleEvent.start.dateTime).getTime();
//       const existingStartTime = new Date(existingEvent.start.dateTime).getTime();
//       const googleEndTime = new Date(googleEvent.end.dateTime).getTime();
//       const existingEndTime = new Date(existingEvent.end.dateTime).getTime();

//       if (googleStartTime !== existingStartTime || googleEndTime !== existingEndTime) {
//         // This is a time change for an existing event
//         rescheduledEvents.push({
//           ...googleEvent,
//           existingEvent,
//           changeType: 'time_change'
//         });
//       } else {
//         // This is an existing event with no time changes
//         newEvents.push(googleEvent);
//       }
//       continue;
//     }

//     // Check if there's an event with the same attendees but different ID
//     let isRescheduled = false;
//     for (const existing of existingEvents) {
//       if (!existing.attendees || existing.attendees.length === 0) continue;

//       const existingAttendeeEmails = existing.attendees
//         .map((a: any) => a.email)
//         .sort()
//         .join(',');

//       if (googleAttendeeEmails === existingAttendeeEmails && existing.id !== googleEvent.id) {
//         // This is likely a rescheduled event (same attendees, different ID)
//         rescheduledEvents.push({
//           ...googleEvent,
//           existingEvent: existing,
//           changeType: 'attendee_match'
//         });
//         isRescheduled = true;
//         break;
//       }
//     }

//     // If not rescheduled, it's a new event
//     if (!isRescheduled) {
//       newEvents.push(googleEvent);
//     }
//   }

//   return {
//     newEvents,
//     rescheduledEvents
//   };
// }

// /**
//  * Formats a warning message for rescheduled events
//  *
//  * @param rescheduledEvents Array of rescheduled events
//  * @returns A formatted warning message
//  */
// export function formatRescheduleWarning(rescheduledEvents: any[]) {
//   if (rescheduledEvents.length === 0) return '';

//   let message = 'WARNING: The following sessions appear to have been rescheduled in Google Calendar:\n\n';

//   rescheduledEvents.forEach((event, index) => {
//     const eventDate = moment(event.start.dateTime).format('MMMM D, YYYY [at] h:mm A');
//     const attendees = event.attendees
//       .map((a: any) => a.email)
//       .join(', ');

//     message += `${index + 1}. "${event.summary}" on ${eventDate}\n`;
//     message += `   Attendees: ${attendees}\n\n`;
//   });

//   message += 'Please note that rescheduling sessions from Google Calendar is not supported. ';
//   message += 'For best results, please use the dashboard to reschedule sessions.\n\n';
//   message += 'These rescheduled sessions will NOT be synced. Only new sessions will be synced.';

//   return message;
// }
