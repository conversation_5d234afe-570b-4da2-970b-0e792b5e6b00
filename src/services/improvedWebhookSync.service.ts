/**
 * Improved Webhook Sync Service
 * 
 * This service provides comprehensive real-time Google Calendar synchronization
 * with proper handling of:
 * - Entire series rescheduling
 * - Partial series rescheduling  
 * - Single session updates
 * - Event deletions
 * - Duplicate prevention
 */

import { CalendarEventDao } from "../lib/dao/calendarEvent.dao";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { GoogleCalendarService } from "./googleCalendar.service";
import { ScheduleStatus } from "../models/Schedule.model";
import { Types } from "mongoose";

export interface IWebhookSyncResult {
  success: boolean;
  processed: number;
  created: number;
  updated: number;
  deleted: number;
  seriesRescheduled: number;
  errors: string[];
}

export class ImprovedWebhookSyncService {
  
  /**
   * Main entry point for processing webhook notifications
   */
  static async processWebhookChange(therapistId: string): Promise<IWebhookSyncResult> {
    const result: IWebhookSyncResult = {
      success: true,
      processed: 0,
      created: 0,
      updated: 0,
      deleted: 0,
      seriesRescheduled: 0,
      errors: []
    };

    try {
      console.log(`🔄 Processing webhook change for therapist ${therapistId}`);

      // Get current Google Calendar events
      const googleEvents = await this.getAllCurrentEvents(therapistId);
      if (!googleEvents || googleEvents.length === 0) {
        console.log(`📭 No Google Calendar events found for therapist ${therapistId}`);
        return result;
      }

      // Get existing database events
      const existingEvents = await CalendarEventDao.getTherapistCalendarEvents(therapistId);
      
      // Process all changes in one comprehensive pass
      await this.processAllChanges(therapistId, googleEvents, existingEvents, result);
      
      console.log(`✅ Webhook processing completed for therapist ${therapistId}:`, {
        created: result.created,
        updated: result.updated,
        deleted: result.deleted,
        seriesRescheduled: result.seriesRescheduled,
        errors: result.errors.length
      });

      return result;

    } catch (error: any) {
      console.error(`❌ Error processing webhook for therapist ${therapistId}:`, error.message);
      result.success = false;
      result.errors.push(error.message);
      return result;
    }
  }

  /**
   * Process all changes in a single comprehensive pass
   */
  private static async processAllChanges(
    therapistId: string,
    googleEvents: any[],
    existingEvents: any[],
    result: IWebhookSyncResult
  ): Promise<void> {
    
    // Create maps for efficient lookup
    const existingEventMap = new Map(existingEvents.map(e => [e.id, e]));
    const googleEventMap = new Map(googleEvents.map(e => [e.id, e]));

    // 1. Handle deleted events (exist in DB but not in Google Calendar)
    await this.handleDeletedEvents(therapistId, existingEvents, googleEventMap, result);

    // 2. Group Google events by series for better series detection
    const eventSeries = this.groupEventsBySeries(googleEvents);
    
    // 3. Handle series rescheduling first (detached events)
    await this.handleSeriesRescheduling(therapistId, eventSeries, existingEventMap, result);

    // 4. Handle individual event updates and new events
    await this.handleIndividualEvents(therapistId, googleEvents, existingEventMap, result);
  }

  /**
   * Handle deleted events
   */
  private static async handleDeletedEvents(
    therapistId: string,
    existingEvents: any[],
    googleEventMap: Map<string, any>,
    result: IWebhookSyncResult
  ): Promise<void> {
    
    for (const existingEvent of existingEvents) {
      if (!googleEventMap.has(existingEvent.id) && existingEvent.status !== 'cancelled') {
        try {
          await this.markEventAsDeleted(therapistId, existingEvent);
          result.deleted++;
          console.log(`🗑️ Deleted event: ${existingEvent.id}`);
        } catch (error: any) {
          result.errors.push(`Failed to delete event ${existingEvent.id}: ${error.message}`);
        }
      }
    }
  }

  /**
   * Handle series rescheduling (detached events)
   */
  private static async handleSeriesRescheduling(
    therapistId: string,
    eventSeries: Map<string, any[]>,
    existingEventMap: Map<string, any>,
    result: IWebhookSyncResult
  ): Promise<void> {
    
    for (const [seriesId, events] of eventSeries) {
      // Check if this series contains detached events (rescheduled)
      const detachedEvents = events.filter(e => this.isDetachedSeriesEvent(e));
      
      if (detachedEvents.length > 0) {
        console.log(`🔄 Processing series reschedule for series ${seriesId}: ${detachedEvents.length} detached events`);
        
        // Determine if it's entire series or partial series rescheduling
        const isEntireSeries = this.isEntireSeriesRescheduled(events, detachedEvents);
        
        if (isEntireSeries) {
          await this.handleEntireSeriesReschedule(therapistId, seriesId, detachedEvents, existingEventMap, result);
        } else {
          await this.handlePartialSeriesReschedule(therapistId, seriesId, detachedEvents, existingEventMap, result);
        }
      }
    }
  }

  /**
   * Handle individual event updates and new events
   */
  private static async handleIndividualEvents(
    therapistId: string,
    googleEvents: any[],
    existingEventMap: Map<string, any>,
    result: IWebhookSyncResult
  ): Promise<void> {
    
    for (const googleEvent of googleEvents) {
      // Skip if this is a detached event (already handled in series processing)
      if (this.isDetachedSeriesEvent(googleEvent)) {
        continue;
      }

      // Skip cancelled events
      if (googleEvent.status === 'cancelled') {
        continue;
      }

      const existingEvent = existingEventMap.get(googleEvent.id);
      
      if (existingEvent) {
        // Check if event was updated
        const googleUpdated = new Date(googleEvent.updated);
        const existingUpdated = new Date(existingEvent.updated);
        
        if (googleUpdated > existingUpdated) {
          try {
            await this.updateExistingEvent(therapistId, existingEvent, googleEvent);
            result.updated++;
            console.log(`📝 Updated event: ${googleEvent.id}`);
          } catch (error: any) {
            result.errors.push(`Failed to update event ${googleEvent.id}: ${error.message}`);
          }
        }
      } else {
        // New event - check for duplicates first
        const isDuplicate = await this.checkForDuplicateSession(therapistId, googleEvent);
        if (!isDuplicate && googleEvent.attendees && googleEvent.attendees.length > 0) {
          try {
            const created = await this.createNewSession(therapistId, googleEvent);
            if (created) {
              result.created++;
              console.log(`➕ Created new session: ${googleEvent.id}`);
            }
          } catch (error: any) {
            result.errors.push(`Failed to create session ${googleEvent.id}: ${error.message}`);
          }
        } else if (isDuplicate) {
          console.log(`⏭️ Skipping duplicate session: ${googleEvent.id}`);
        }
      }
      
      result.processed++;
    }
  }

  /**
   * Handle entire series rescheduling
   */
  private static async handleEntireSeriesReschedule(
    therapistId: string,
    seriesId: string,
    detachedEvents: any[],
    existingEventMap: Map<string, any>,
    result: IWebhookSyncResult
  ): Promise<void> {
    
    console.log(`🔄 Handling entire series reschedule for series ${seriesId}`);
    
    // Find the original series events in the database
    const originalEvents = await this.findOriginalSeriesEvents(therapistId, seriesId, detachedEvents);
    
    if (originalEvents.length === 0) {
      console.warn(`⚠️ No original events found for series ${seriesId}`);
      return;
    }

    // Update each original event with the new detached event data
    for (let i = 0; i < Math.min(originalEvents.length, detachedEvents.length); i++) {
      const originalEvent = originalEvents[i];
      const detachedEvent = detachedEvents[i];
      
      try {
        await this.updateSeriesEvent(therapistId, originalEvent, detachedEvent);
        result.seriesRescheduled++;
        console.log(`🔄 Updated series event: ${originalEvent.id} -> ${detachedEvent.id}`);
      } catch (error: any) {
        result.errors.push(`Failed to update series event ${originalEvent.id}: ${error.message}`);
      }
    }
  }

  /**
   * Handle partial series rescheduling
   */
  private static async handlePartialSeriesReschedule(
    therapistId: string,
    seriesId: string,
    detachedEvents: any[],
    existingEventMap: Map<string, any>,
    result: IWebhookSyncResult
  ): Promise<void> {
    
    console.log(`🔄 Handling partial series reschedule for series ${seriesId}`);
    
    // For each detached event, find its corresponding original event and update it
    for (const detachedEvent of detachedEvents) {
      try {
        const originalEvent = await this.findMatchingOriginalEvent(therapistId, detachedEvent);
        
        if (originalEvent) {
          await this.updateSeriesEvent(therapistId, originalEvent, detachedEvent);
          result.seriesRescheduled++;
          console.log(`🔄 Updated partial series event: ${originalEvent.id} -> ${detachedEvent.id}`);
        } else {
          console.warn(`⚠️ No matching original event found for detached event ${detachedEvent.id}`);
        }
      } catch (error: any) {
        result.errors.push(`Failed to update partial series event ${detachedEvent.id}: ${error.message}`);
      }
    }
  }

  /**
   * Get all current events from Google Calendar
   */
  private static async getAllCurrentEvents(therapistId: string): Promise<any[]> {
    try {
      // Get events from the last month to next 6 months for comprehensive analysis
      const timeMin = new Date();
      timeMin.setMonth(timeMin.getMonth() - 1);

      const timeMax = new Date();
      timeMax.setMonth(timeMax.getMonth() + 6);

      const events = await GoogleCalendarService.eventByDate(
        therapistId,
        500, // Higher limit for comprehensive sync
        timeMin,
        timeMax
      );

      return events || [];
    } catch (error: any) {
      console.error(`Error getting all current events for therapist ${therapistId}:`, error.message);
      return [];
    }
  }

  /**
   * Group events by series (iCalUID)
   */
  private static groupEventsBySeries(events: any[]): Map<string, any[]> {
    const seriesMap = new Map<string, any[]>();
    
    for (const event of events) {
      const seriesId = event.iCalUID || event.id;
      if (!seriesMap.has(seriesId)) {
        seriesMap.set(seriesId, []);
      }
      seriesMap.get(seriesId)!.push(event);
    }
    
    return seriesMap;
  }

  /**
   * Check if event is a detached series event (rescheduled from Google Calendar)
   */
  private static isDetachedSeriesEvent(event: any): boolean {
    // Check for detached ID pattern (contains underscore with timestamp)
    const detachedIdPattern = /^[a-zA-Z0-9]+_\d{8}T\d{6}Z$/;
    return detachedIdPattern.test(event.id);
  }

  /**
   * Determine if entire series was rescheduled or just partial
   */
  private static isEntireSeriesRescheduled(allEvents: any[], detachedEvents: any[]): boolean {
    // If more than 80% of events are detached, consider it entire series
    const detachedRatio = detachedEvents.length / allEvents.length;
    return detachedRatio > 0.8;
  }

  /**
   * Find original series events in database that match the detached events
   */
  private static async findOriginalSeriesEvents(
    therapistId: string,
    seriesId: string,
    detachedEvents: any[]
  ): Promise<any[]> {
    try {
      // Get all events for this therapist
      const allEvents = await CalendarEventDao.getTherapistCalendarEvents(therapistId);

      // Find events that belong to the same series
      const seriesEvents = allEvents.filter(event => {
        // Check if iCalUID matches or if it's related to the same series
        return event.iCalUID === seriesId ||
               (event.id && event.id.startsWith(seriesId.split('_')[0]));
      });

      // Sort by start time to match with detached events
      return seriesEvents.sort((a, b) => {
        const aStart = new Date(a.start?.dateTime || a.start?.date);
        const bStart = new Date(b.start?.dateTime || b.start?.date);
        return aStart.getTime() - bStart.getTime();
      });
    } catch (error: any) {
      console.error(`Error finding original series events for ${seriesId}:`, error.message);
      return [];
    }
  }

  /**
   * Find matching original event for a detached event
   */
  private static async findMatchingOriginalEvent(
    therapistId: string,
    detachedEvent: any
  ): Promise<any | null> {
    try {
      // Extract base ID from detached ID
      const baseId = detachedEvent.id.split('_')[0];

      // Get all events for this therapist
      const allEvents = await CalendarEventDao.getTherapistCalendarEvents(therapistId);

      // Look for matching events based on various criteria
      const matchingEvent = allEvents.find(event => {
        // 1. Check if base ID matches
        if (event.id === baseId) return true;

        // 2. Check if it's part of the same series
        if (event.iCalUID === detachedEvent.iCalUID) {
          // Compare attendees and summary for additional verification
          return this.eventsMatch(event, detachedEvent);
        }

        return false;
      });

      return matchingEvent || null;
    } catch (error: any) {
      console.error(`Error finding matching original event for ${detachedEvent.id}:`, error.message);
      return null;
    }
  }

  /**
   * Check if two events match (same attendees and similar summary)
   */
  private static eventsMatch(event1: any, event2: any): boolean {
    // Compare attendees
    const attendees1 = (event1.attendees || []).map((a: any) => a.email).sort();
    const attendees2 = (event2.attendees || []).map((a: any) => a.email).sort();

    if (JSON.stringify(attendees1) !== JSON.stringify(attendees2)) {
      return false;
    }

    // Compare summary similarity
    if (event1.summary && event2.summary) {
      const summary1 = event1.summary.toLowerCase();
      const summary2 = event2.summary.toLowerCase();
      return summary1.includes(summary2) || summary2.includes(summary1);
    }

    return true;
  }

  /**
   * Update a series event with new detached event data
   */
  private static async updateSeriesEvent(
    therapistId: string,
    originalEvent: any,
    detachedEvent: any
  ): Promise<void> {
    try {
      // Update the calendar event
      originalEvent.start = detachedEvent.start;
      originalEvent.end = detachedEvent.end;
      originalEvent.updated = new Date(detachedEvent.updated);
      originalEvent.id = detachedEvent.id; // Update to new detached ID
      originalEvent.summary = detachedEvent.summary;
      originalEvent.description = detachedEvent.description;
      originalEvent.location = detachedEvent.location;

      // Mark as rescheduled
      (originalEvent as any).isRescheduled = true;
      (originalEvent as any).originalEventId = originalEvent.id;
      (originalEvent as any).rescheduledAt = new Date();

      await originalEvent.save();

      // Update the corresponding schedule
      const schedule = await ScheduleDao.getScheduleById(originalEvent.scheduleId);
      if (schedule) {
        const recurrenceDate = schedule.recurrenceDates.find(
          (rd: any) => rd.calenderEventId && rd.calenderEventId.toString() === originalEvent._id.toString()
        );

        if (recurrenceDate) {
          // Update timing
          recurrenceDate.fromDate = new Date(detachedEvent.start.dateTime || detachedEvent.start.date);
          recurrenceDate.toDate = new Date(detachedEvent.end.dateTime || detachedEvent.end.date);

          // Mark as rescheduled
          recurrenceDate.status = ScheduleStatus.RESCHEDULED;
          (recurrenceDate as any).other = {
            ...((recurrenceDate as any).other || {}),
            isRescheduled: true,
            rescheduledAt: new Date(),
            rescheduledFrom: 'google_calendar'
          };

          await schedule.save();
        }
      }

      console.log(`✅ Successfully updated series event: ${originalEvent.id} -> ${detachedEvent.id}`);
    } catch (error: any) {
      console.error(`Error updating series event ${originalEvent.id}:`, error.message);
      throw error;
    }
  }

  /**
   * Update existing event with new data
   */
  private static async updateExistingEvent(
    therapistId: string,
    existingEvent: any,
    googleEvent: any
  ): Promise<void> {
    try {
      // Update calendar event
      existingEvent.start = googleEvent.start;
      existingEvent.end = googleEvent.end;
      existingEvent.updated = new Date(googleEvent.updated);
      existingEvent.summary = googleEvent.summary;
      existingEvent.description = googleEvent.description;
      existingEvent.location = googleEvent.location;
      existingEvent.attendees = googleEvent.attendees;

      await existingEvent.save();

      // Update corresponding schedule
      const schedule = await ScheduleDao.getScheduleById(existingEvent.scheduleId);
      if (schedule) {
        const recurrenceDate = schedule.recurrenceDates.find(
          (rd: any) => rd.calenderEventId && rd.calenderEventId.toString() === existingEvent._id.toString()
        );

        if (recurrenceDate) {
          recurrenceDate.fromDate = new Date(googleEvent.start.dateTime || googleEvent.start.date);
          recurrenceDate.toDate = new Date(googleEvent.end.dateTime || googleEvent.end.date);

          await schedule.save();
        }
      }
    } catch (error: any) {
      console.error(`Error updating existing event ${existingEvent.id}:`, error.message);
      throw error;
    }
  }

  /**
   * Mark event as deleted and cancel corresponding sessions
   */
  private static async markEventAsDeleted(therapistId: string, calendarEvent: any): Promise<void> {
    try {
      // Find the schedule associated with this calendar event
      const schedule = await ScheduleDao.getScheduleById(calendarEvent.scheduleId);
      if (!schedule) {
        console.log(`No schedule found for deleted calendar event: ${calendarEvent.id}`);
        return;
      }

      // Find the specific recurrence date
      const recurrenceDate = schedule.recurrenceDates.find(
        (rd: any) => rd.calenderEventId && rd.calenderEventId.toString() === calendarEvent._id.toString()
      );

      if (recurrenceDate) {
        // Mark the specific session as cancelled
        recurrenceDate.status = ScheduleStatus.CANCELLED;
        (recurrenceDate as any).other = {
          ...((recurrenceDate as any).other || {}),
          isRefunded: false,
          isRescheduled: false,
          cancelledAt: new Date(),
          cancelReason: 'Deleted from Google Calendar'
        };

        await schedule.save();
        console.log(`Marked session as cancelled for deleted event: ${calendarEvent.id}`);
      }

      // Update the calendar event status
      calendarEvent.status = 'cancelled';
      (calendarEvent as any).deletedAt = new Date();
      await calendarEvent.save();

    } catch (error: any) {
      console.error(`Error marking event as deleted ${calendarEvent.id}:`, error.message);
      throw error;
    }
  }

  /**
   * Create new session from Google Calendar event
   */
  private static async createNewSession(therapistId: string, event: any): Promise<boolean> {
    try {
      // Extract attendee email (first attendee that's not the organizer)
      const attendeeEmail = event.attendees?.find((attendee: any) =>
        attendee.email !== event.organizer?.email
      )?.email;

      if (!attendeeEmail) {
        console.log(`No valid attendee email found for event ${event.id}`);
        return false;
      }

      // Create schedule data from Google Calendar event
      const scheduleData = {
        therapistId: therapistId,
        email: attendeeEmail,
        name: event.summary || "Google Calendar Session",
        description: event.description || "",
        fromPublicCalender: true,
        syncWithCalender: true,
        location: event.location || "online",
        recurrenceDates: [{
          fromDate: new Date(event.start.dateTime || event.start.date),
          toDate: new Date(event.end.dateTime || event.end.date),
          status: ScheduleStatus.CONFIRMED,
          syncStatus: true
        }],
        scheduleId: `gc_${event.id}_${Date.now()}` // Unique schedule ID
      };

      // Create the schedule
      const schedule = await ScheduleDao.createSchedule(scheduleData);
      if (!schedule) {
        console.log(`Failed to create schedule for event ${event.id}`);
        return false;
      }

      // Create calendar event record
      const calendarEventData = {
        therapistId: therapistId,
        scheduleId: schedule._id,
        scheduleRecId: schedule.recurrenceDates[0]._id,
        etag: event.etag,
        id: event.id,
        status: event.status,
        htmlLink: event.htmlLink,
        created: new Date(event.created),
        updated: new Date(event.updated),
        summary: event.summary,
        description: event.description,
        location: event.location,
        creator: event.creator,
        organizer: event.organizer,
        start: event.start,
        end: event.end,
        iCalUID: event.iCalUID,
        sequence: event.sequence,
        attendees: event.attendees,
        reminders: event.reminders,
        eventType: event.eventType,
        hangoutLink: event.hangoutLink,
        conferenceData: event.conferenceData,
        visibility: event.visibility
      };

      const calendarEvent = await CalendarEventDao.createCalendarEvent(calendarEventData);
      if (calendarEvent) {
        // Link the calendar event to the schedule
        schedule.recurrenceDates[0].calenderEventId = calendarEvent._id;
        await schedule.save();

        console.log(`Successfully created session from Google Calendar event ${event.id}`);
        return true;
      }

      return false;
    } catch (error: any) {
      console.error(`Error creating session from Google event ${event.id}:`, error.message);
      return false;
    }
  }

  /**
   * Check for duplicate sessions and rescheduled sessions to prevent creating duplicates
   */
  private static async checkForDuplicateSession(therapistId: string, event: any): Promise<boolean> {
    try {
      // Get all existing schedules for this therapist
      const existingSchedules = await ScheduleDao.getScheduleByTherapistId(therapistId);

      const eventStart = new Date(event.start.dateTime || event.start.date);
      const eventEnd = new Date(event.end.dateTime || event.end.date);
      const eventAttendees = (event.attendees || []).map((a: any) => a.email).sort();

      for (const schedule of existingSchedules) {
        for (const recurrenceDate of schedule.recurrenceDates) {
          // Skip cancelled sessions
          if (recurrenceDate.status === ScheduleStatus.CANCELLED) {
            continue;
          }

          // Check if timing and attendees match (exact duplicate)
          const scheduleStart = new Date(recurrenceDate.fromDate);
          const scheduleEnd = new Date(recurrenceDate.toDate);

          // Check if times match (within 1 minute tolerance)
          const startTimeDiff = Math.abs(eventStart.getTime() - scheduleStart.getTime());
          const endTimeDiff = Math.abs(eventEnd.getTime() - scheduleEnd.getTime());

          if (startTimeDiff < 60000 && endTimeDiff < 60000) { // 1 minute tolerance
            // Check if attendees match
            const scheduleAttendees = [schedule.email, ...(schedule.additionalEmails || [])].sort();

            if (JSON.stringify(eventAttendees) === JSON.stringify(scheduleAttendees)) {
              console.log(`Duplicate session detected for event ${event.id} - matches existing session`);
              return true;
            }
          }

          // Check if this is a rescheduled version of an existing session
          if (await this.isRescheduledSession(event, schedule, recurrenceDate, eventAttendees)) {
            console.log(`Rescheduled session detected for event ${event.id} - updating existing session instead of creating new`);
            await this.updateRescheduledSession(therapistId, event, schedule, recurrenceDate);
            return true; // Treat as duplicate to prevent new session creation
          }
        }
      }

      return false;
    } catch (error: any) {
      console.error(`Error checking for duplicate session ${event.id}:`, error.message);
      return false;
    }
  }

  /**
   * Check if a Google Calendar event is a rescheduled version of an existing session
   */
  private static async isRescheduledSession(
    event: any,
    schedule: any,
    recurrenceDate: any,
    eventAttendees: string[]
  ): Promise<boolean> {
    try {
      // Check if attendees match
      const scheduleAttendees = [schedule.email, ...(schedule.additionalEmails || [])].sort();
      if (JSON.stringify(eventAttendees) !== JSON.stringify(scheduleAttendees)) {
        return false;
      }

      // Check if the session is already marked as rescheduled
      if (recurrenceDate.status !== ScheduleStatus.RESCHEDULED) {
        return false;
      }

      // Check if the event has a detached ID pattern (indicates rescheduling in Google Calendar)
      const detachedIdPattern = /^[a-zA-Z0-9]+_\d{8}T\d{6}Z/;
      if (!detachedIdPattern.test(event.id)) {
        return false;
      }

      // Check if the event summary/title is similar
      const eventSummary = (event.summary || '').toLowerCase();
      const scheduleName = (schedule.name || '').toLowerCase();
      const scheduleDescription = (schedule.description || '').toLowerCase();

      if (eventSummary && (
        eventSummary.includes(scheduleName) ||
        scheduleName.includes(eventSummary) ||
        eventSummary.includes(scheduleDescription) ||
        scheduleDescription.includes(eventSummary)
      )) {
        return true;
      }

      // Check if there's an existing calendar event with similar base ID
      const baseEventId = event.id.split('_')[0];
      const existingCalendarEvent = await CalendarEventDao.findByGoogleEventId(baseEventId);
      if (existingCalendarEvent && existingCalendarEvent.scheduleId.toString() === schedule._id.toString()) {
        return true;
      }

      return false;
    } catch (error: any) {
      console.error(`Error checking if session is rescheduled:`, error.message);
      return false;
    }
  }

  /**
   * Update an existing rescheduled session with new Google Calendar data
   */
  private static async updateRescheduledSession(
    therapistId: string,
    event: any,
    schedule: any,
    recurrenceDate: any
  ): Promise<void> {
    try {
      // Update the recurrence date with new timing
      recurrenceDate.fromDate = new Date(event.start.dateTime || event.start.date);
      recurrenceDate.toDate = new Date(event.end.dateTime || event.end.date);
      recurrenceDate.status = ScheduleStatus.CONFIRMED; // Change from rescheduled to confirmed
      recurrenceDate.syncStatus = true;

      // Update the other field to track the rescheduling
      if (!recurrenceDate.other) {
        recurrenceDate.other = {};
      }
      recurrenceDate.other.isRescheduled = true;
      recurrenceDate.other.rescheduledAt = new Date();
      recurrenceDate.other.rescheduledFrom = 'google_calendar';

      await schedule.save();

      // Create or update calendar event record
      let calendarEvent = await CalendarEventDao.findByScheduleAndRecurrenceId(schedule._id, recurrenceDate._id);

      if (!calendarEvent) {
        // Create new calendar event record
        const calendarEventData = {
          therapistId: therapistId,
          scheduleId: schedule._id,
          scheduleRecId: recurrenceDate._id,
          etag: event.etag,
          id: event.id,
          status: event.status,
          htmlLink: event.htmlLink,
          created: new Date(event.created),
          updated: new Date(event.updated),
          summary: event.summary,
          description: event.description,
          location: event.location,
          creator: event.creator,
          organizer: event.organizer,
          start: event.start,
          end: event.end,
          iCalUID: event.iCalUID,
          sequence: event.sequence,
          attendees: event.attendees,
          reminders: event.reminders,
          eventType: event.eventType,
          hangoutLink: event.hangoutLink,
          conferenceData: event.conferenceData,
          visibility: event.visibility
        };

        calendarEvent = await CalendarEventDao.createCalendarEvent(calendarEventData);
        if (calendarEvent) {
          recurrenceDate.calenderEventId = calendarEvent._id;
          await schedule.save();
        }
      } else {
        // Update existing calendar event
        calendarEvent.id = event.id; // Update to new detached ID
        calendarEvent.start = event.start;
        calendarEvent.end = event.end;
        calendarEvent.updated = new Date(event.updated);
        calendarEvent.summary = event.summary;
        calendarEvent.description = event.description;
        calendarEvent.location = event.location;
        calendarEvent.attendees = event.attendees;
        await calendarEvent.save();
      }

      console.log(`✅ Successfully updated rescheduled session: ${event.id}`);
    } catch (error: any) {
      console.error(`Error updating rescheduled session ${event.id}:`, error.message);
      throw error;
    }
  }

  /**
   * Disable automatic cron sync when webhooks are active
   */
  static async shouldDisableCronSync(therapistId: string): Promise<boolean> {
    try {
      const CalendarWebhookModel = require('../models/CalendarWebhook.model').CalendarWebhookModel;
      const activeWebhook = await CalendarWebhookModel.findOne({
        therapistId,
        isActive: true,
        expiration: { $gt: new Date() }
      });

      return !!activeWebhook;
    } catch (error: any) {
      console.error(`Error checking webhook status for therapist ${therapistId}:`, error.message);
      return false;
    }
  }
}
