import { PayoutDao } from "../lib/dao/payout.dao";
import { PayoutStatus } from "../models/payouts.model";

export class PayoutService{
    static async getAll(pageSize: any, skip: any, therapistId?: any, status?: any){
        return await PayoutDao.getAll(pageSize, skip, therapistId, status)
    }

    static async create(payoutData: any){
        return await PayoutDao.create(payoutData)
    }

    static async getPayoutById(payoutId: any, populated?: any){
        if(populated) return await PayoutDao.payoutIdPopulated(payoutId);
        return await PayoutDao.payoutId(payoutId)
    }

    static async deletePayout(payoutId: any){
        return await PayoutDao.deletePayout(payoutId)
    }

    static async updatePayout(payoutId: any, payoutData: any){
        return await PayoutDao.updatePayout(payoutId, payoutData)
    }

    static async getStatusCount(days: number, status: PayoutStatus){
        return await PayoutDao.getStatusCount(days, status);
    }

    static async getCount(days: number){
        return await PayoutDao.getCount(days);
    }
    static async getPayoutAmount(days: number){
        return await PayoutDao.getPayoutAmount(days);
    }

    static async getTherapistPayouts(therapistId: any, status: PayoutStatus){
        return await PayoutDao.getTherapistPayouts(therapistId, status);
    }
}