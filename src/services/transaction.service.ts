import moment from "moment";
import { TransactionDao } from "../lib/dao/transaction.dao";
import { throwError } from "../util/response";
import { paymentStatus } from "../lib/enum/cashfree.enum";
import RazorpayService from "./razorpay.service";
import { ScheduleDao } from "../lib/dao/schedule.dao";

export class TransactionService{
    static async getAll(pageSize: any, skip: any, therapistId?: any, status?: any, fromDate?: any, toDate?: any){
        return await TransactionDao.getAll(pageSize, skip, therapistId, status, fromDate, toDate)
    }

    static async getAllCount(therapistId?: any, status?: any, fromDate?: any, toDate?: any){
        return await TransactionDao.getAllCount(therapistId, status, fromDate, toDate)
    }
    static async createTransaction(therapist: any, scheduleId: any,  amount: number, clientId?: any, recurrenceDateId?: any){
        return await TransactionDao.createTransaction(therapist, scheduleId,  amount, clientId, recurrenceDateId); 
    }

    static async createRzpTransaction(therapistId: any, scheduleId: any, amount: number, clientId?: any, recurrenceDateId?: any){
        return await TransactionDao.createRzpTransaction(therapistId, scheduleId, amount, clientId, recurrenceDateId); 
    }

    static async getTransactionDetails(therapistId: any, scheduleId: any, clientId?: any, recurrenceDateId?: any){
        return await TransactionDao.getTransactionDetails(therapistId, scheduleId, clientId, recurrenceDateId); 
    }

    static async getTherapistFromTransaction(therapistId: any ) {
        return await TransactionDao.getTherapistFromTransaction(therapistId); 
    }

    static async getAllPopulatedTransactionsofTherapist(therapistId: any) {
        return await TransactionDao.getAllPopulatedTransactionsofTherapist(therapistId); 
    }

    static async saveTransactionDetails(payload: any, transactionId: any) {
        return await TransactionDao.saveTransactionDetails(payload, transactionId); 
    }

    // static async getTransactionWithCustomerId(customerId: any, paymentSessionId: string) {
    //     return await TransactionDao.getTransactionWithCustomerId(customerId, paymentSessionId); 
    // }

    static async paymentLinkUpdate(transactionId: any, paymentLink: string, paymentSessionId: string) {
        return await TransactionDao.paymentLinkUpdate(transactionId, paymentLink, paymentSessionId); 
    }

    static async getTransactionById(transactionId: any) {
        return await TransactionDao.getTransactionById(transactionId); 
    }

    static async getTransactionBy_Id(transactionId: any, therapistId: any) {
        return await TransactionDao.getTransactionBy_Id(transactionId, therapistId); 
    }

    static async getTransactionBy_id(transactionId: any) {
        return await TransactionDao.getTransactionBy_id(transactionId); 
    }

    static async getTherapistTransactions(therapistId: any, clientId?: any) {
        return await TransactionDao.getTherapistTransactions(therapistId, clientId); 
    }

    static async getTherapistTransactionByStatus(therapistId: any, status: any) {
        return await TransactionDao.getTherapistTransactionByStatus(therapistId, status); 
    }

    static async getTherapistPendingTransactions(therapistId: any) {
        return await TransactionDao.getTherapistPendingTransactions(therapistId); 
    }
    
    static async getTherapistCompleteTransactions(therapistId: any) {
        return await TransactionDao.getTherapistCompleteTransactions(therapistId); 
    }

    static async getTherapistMarkPaidOfflineTransactionset(therapistId: any) {
        return await TransactionDao.getTherapistMarkPaidOfflineTransactions(therapistId); 
    }

    static async getTransactionsOfAMonth(therapistId: any, firstDayOfMonth: any, lastDayOfMonth: any) {
        return await TransactionDao.getTransactionsOfAMonth(therapistId, firstDayOfMonth, lastDayOfMonth); 
    }

    static async getAllTransactions(therapistId: any, skip: number, pageSize: number) {
        return await TransactionDao.getAllTransactions(therapistId, skip, pageSize); 
    }

    static async getCount(days: number) {
        return await TransactionDao.getCount(days); 
    }
    static async getPendingCount(days: number) {
        return await TransactionDao.getPendingCount(days); 
    }

    
    static async getPayments(payments: any, name: any, startDate: any, endDate: any, amount: any, skip: any, pageSize: any) {
        let count;
        let requiredPayments;
        if (name && !startDate && !endDate && !amount) {
            const nameFiltered = payments.sort((a: any, b: any) => a?.clientName.localeCompare(b?.clientName));
            count = nameFiltered.length;
            requiredPayments = nameFiltered.slice(skip, skip + pageSize);
        }
        if (!name && startDate && endDate && !amount) {
            const dateFiltered = payments.filter((payment: any) => moment(payment.sessionDate) >= moment(startDate) && moment(payment.sessionDate) <= moment(endDate));
            count = dateFiltered.length;
            const dateFilteredData = dateFiltered.sort((a: any, b: any) => b["sessionDate"] - a["sessionDate"]);
            requiredPayments = dateFilteredData.slice(skip, skip + pageSize);
        }
        if (name && startDate && endDate && !amount) {
            const dateFiltered = payments.filter((payment: any) => moment(payment.sessionDate) >= moment(startDate) && moment(payment.sessionDate) <= moment(endDate));
            count = dateFiltered.length;
            const nameFiltered = dateFiltered.sort((a: any, b: any) => a?.clientName.localeCompare(b?.clientName));
            requiredPayments = nameFiltered.slice(skip, skip + pageSize);
        }
        if (!name && startDate && endDate && amount) {
            const dateFiltered = payments.filter((payment: any) => moment(payment.sessionDate) >= moment(startDate) && moment(payment.sessionDate) <= moment(endDate));
            count = dateFiltered.length;
            const amountFiltered = dateFiltered.sort((a: any, b: any) => Number(b["amount"]) - Number(a["amount"]));
            requiredPayments = amountFiltered.slice(skip, skip + pageSize);
        }
        if (!name && !startDate && !endDate && amount) {
            const amountFiltered = payments.sort((a: any, b: any) => Number(b["amount"]) - Number(a["amount"]));
            count = amountFiltered.length;
            requiredPayments = amountFiltered.slice(skip, skip + pageSize);
        }
        if (!name && !startDate && !endDate && !amount) {
            count = payments.length;
            const recent = payments.sort((a: any, b: any) => a["sessionDate"] - b["sessionDate"]);
            requiredPayments = recent.slice(skip, skip + pageSize);
        }
        return {
            count, requiredPayments
        }
    } 

    static async getTransactionByRecId(scheduleRecId: any, therapistId: any) {
        return await TransactionDao.getTransactionByRecId(scheduleRecId, therapistId); 
    }

    static async isTransactionComplete(scheduleRecId: any, therapistId: any) {
        return await TransactionDao.isTransactionComplete(scheduleRecId, therapistId); 
    }

    static async isPaymentPendingBefore48Hours(scheduleRecId: any, therapistId: any, twoDaysBeforeSessionStart: any) {
        return await TransactionDao.isPaymentPendingBefore48Hours(scheduleRecId, therapistId, twoDaysBeforeSessionStart); 
    }

    static async cancelPayment(transactionId: any, therapistId: any) {
        return await TransactionDao.cancelPayment(transactionId, therapistId); 
    }

    static async getLastTransactionOfClient(clientId: any, therapistId:any){
        return await TransactionDao.getLastTransactionOfClient(clientId, therapistId);
    }

    static async getTransactionByRecIdTherapistId(scheduleRecId:any,therpaistId:any){
        return await TransactionDao.getTransactionByRecIdTherapistId(scheduleRecId,therpaistId);
    }

    static async updateTransactionByRecIdTherapistId(scheduleRecId:any,therpaistId:any,payload:any){
        return await TransactionDao.updateTransactionByRecIdTherapistId(scheduleRecId,therpaistId,payload);
    }

    static async getTherapistPendingTransactionsInMonth(therapistId: any, firstDayOfMonth: any, lastDayOfMonth: any) {
        return await TransactionDao.getTherapistPendingTransactionsInMonth(therapistId, firstDayOfMonth, lastDayOfMonth); 
    }

    static async markPaidOffline(transactionId: any, therapistId: any) {
        const transaction = await TransactionService.getTransactionBy_Id(transactionId, therapistId);
        if (!transaction) return throwError("No transaction found - Session would be auto complete once time is crossed", 404);

        switch (transaction.paymentStatus) {
            case paymentStatus.COMPLETE:
            case paymentStatus.PAID_OFFLINE:
            case paymentStatus.CANCELLED:
                throwError("Unable to Mark Offline! Current Payment status is " + transaction.paymentStatus, 403);
                return false;
            case paymentStatus.PENDING: {
                const cancelLink = await RazorpayService.cancelPaymentLink(transaction.paymentSessionId);
                if (!cancelLink) return throwError("Unable to cancel link. - RZP", 400);

                const updateScheduleRecAmount = await ScheduleDao.updateRecurrenceAmountZero(therapistId, transaction.scheduleId, transaction.scheduleRecId, 0);
                if (!updateScheduleRecAmount) return throwError("Unable to update schedule amount", 500);

                const updateTransaction = await TransactionDao.markTransactionPaidOffline(transaction._id);
                if (!updateTransaction) return throwError("Unable to update transaction", 500);
            }
        }
        return true;
    }

}   