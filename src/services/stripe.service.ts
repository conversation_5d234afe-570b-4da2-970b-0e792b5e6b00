// import { CONFIG } from "../config/environment";
// import { RazorpayUtility } from "../util/razorpay";
// import { throwError } from "../util/response";
// import { TransactionService } from "./transaction.service";

// const stripe = require('stripe')(CONFIG.stripe.stripeSecretKEy);


// export class StripeService {
//     static async createPaymentLink(amount: number, therapist: any, scheduleId?: any, clientId?: any, recurrenceDateId?: any) {
//         // let customer: any;
//         // const therapistFromTransaction: any = await TransactionService.getTherapistFromTransaction(therapist._id);
//         // if (!therapistFromTransaction || !therapistFromTransaction.customerId) {
//         //     customer = await stripe.customers.create({
//         //         email: therapist.email,
//         //         name: therapist.name,
//         //     });
//         // } else {
//         //     customer = {
//         //         id: therapistFromTransaction.customerId,
//         //     }            
//         // }

//         // const therapistTransaction = await TransactionService.createTransaction(therapist, scheduleId, customer.id, amount, clientId, recurrenceDateId );
//         // console.log(amount);
//         const therapistTransaction = await TransactionService.createRzpTransaction(therapist, scheduleId, amount, clientId, recurrenceDateId );
//         // const paymentLink = await stripe.checkout.sessions.create({
//         //     customer: customer.id,
//         //     payment_method_types: ['card'],
//         //     line_items: [{
//         //         price_data: {
//         //             currency: CONFIG.stripe.stripeCurreny,
//         //             product_data: {
//         //                 name: CONFIG.stripe.stringProductName,
//         //             },
//         //             unit_amount: amount * 100, // $10.00
//         //         },
//         //         quantity: 1,
//         //     }],
//         //     mode: 'payment',
//         //     success_url: CONFIG.stripe.paymentSuccess,
//         //     cancel_url: CONFIG.stripe.paymentCancel,
//         // });
//         const paymentLink = await RazorpayUtility.createPaymentLink(amount, therapist, therapistTransaction._id)

        // const paymentLinkUpdate = await TransactionService.paymentLinkUpdate(therapistTransaction._id, paymentLink.short_url, paymentLink.id); 

//         return {paymentLink: paymentLink.short_url, transactionId: therapistTransaction._id};
//     }

//     static async refundPayment(transactionId: any) {
//         try {
//             const transaction: any = await TransactionService.getTransactionBy_Id(transactionId); 
//             if (!transaction) {
//                 throwError("no transaction found", 404); 
//             }
//             // Retrieve the session
//             const session = await stripe.checkout.sessions.retrieve(transaction.paymentSessionId);
//             const paymentIntentId = session.payment_intent;
    
//             // Create a refund
//             const refund = await stripe.refunds.create({
//                 payment_intent: paymentIntentId,
//             });
    
//             return refund;
//         } catch (error) {
//             console.error('Refund failed:', error);
//             throw error;
//         }
//     }

//     static async recreatePaymentLink(amount: number, transaction: any, therapist: any) {
//         // const customerId = transaction.customerId
//         // const paymentLink = await stripe.checkout.sessions.create({
//         //     customer: customerId,
//         //     payment_method_types: ['card'],
//         //     line_items: [{
//         //         price_data: {
//         //             currency: CONFIG.stripe.stripeCurreny,
//         //             product_data: {
//         //                 name: CONFIG.stripe.stringProductName,
//         //             },
//         //             unit_amount: amount * 100, // $10.00
//         //         },
//         //         quantity: 1,
//         //     }],
//         //     mode: 'payment',
//         //     success_url: CONFIG.stripe.paymentSuccess,
//         //     cancel_url: CONFIG.stripe.paymentCancel,
//         // });
//         const paymentLink = await RazorpayUtility.createPaymentLink(amount, therapist, transaction._id)

//         const paymentLinkUpdate = await TransactionService.paymentLinkUpdate(transaction._id, paymentLink.short_url, paymentLink.id); 

//         return {paymentLink: paymentLink.short_url, transactionId: transaction._id};
//     }
// }
