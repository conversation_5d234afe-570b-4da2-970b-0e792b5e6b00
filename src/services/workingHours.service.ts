import { WorkingHoursDao } from "../lib/dao/workingHours.dao";

export class WorkingHoursService {
  static async createWorkingHours(payload: any) {
    return await WorkingHoursDao.createWorkingHours(payload);
  }

  static async updateWorkingHours(payload: any, therapistId: string) {
    return await WorkingHoursDao.updateWorkingHours(payload, therapistId);
  }

  static async getTherapistWorkingHours(id: string) {
    return await WorkingHoursDao.getTherapistWorkingHours(id);
  }

  static async createSpecificWorkingHours(
    payload: any,
    isSlotsAvailable: boolean
  ) {
    return await WorkingHoursDao.createSpecificWorkingHours(
      payload,
      isSlotsAvailable
    );
  }

  static async getSpecificWorkingHoursById(id: string) {
    return await WorkingHoursDao.getSpecificWorkingHoursById(id);
  }

  static async updateSpecificWorkingHours(payload: any, therapistId: string) {
    return await WorkingHoursDao.updateSpecificWorkingHours(
      payload,
      therapistId
    );
  }

  static async getSpecificWorkingHoursByDate(
    therapistId: string,
    date: string
  ) {
    return await WorkingHoursDao.getSpecificWorkingHoursByDate(
      therapistId,
      date
    );
  }

  static async getTherapistSpecificWorkingHours(getCondition: any) {
    return await WorkingHoursDao.getTherapistSpecificWorkingHours(getCondition);
  }

  static async getTherapistAllWorkingHours(
    therapistId: string,
    durationCondition: any
  ) {
    return await WorkingHoursDao.getTherapistAllWorkingHours(
      therapistId,
      durationCondition
    );
  }

  static async getTherapistAllSpecificWorkingHours(condition: any) {
    return await WorkingHoursDao.getTherapistAllSpecificWorkingHours(condition);
  }
}
