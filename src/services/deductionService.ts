import { DeductionDao } from "../lib/dao/deduction.dao";

export class DeductionService {
    static async createDeduction(adminId: any, therapistId: any, amount: number, deductionType: any, deductionDate: any) {
        return await DeductionDao.createDeduction(
            adminId,
            therapistId,
            amount,
            deductionType,
            deductionDate
        )
    }

    static async createRefundDeduction(therapistId: any, amount: number, scheduleId: any, scheduleRecId: any, clientId: any) {
        return await DeductionDao.createRefundDeduction(
            therapistId,
            amount,
            scheduleId,
            scheduleRecId,
            clientId
        )
    }

    static async getAllDecution(pageSize: any, skip: any, therapistId: any, isNotPaid: any) {
        return await DeductionDao.getAllDecution(pageSize, skip, therapistId, isNotPaid);
    }

    static async getDeductionById(id: any) {
        return await DeductionDao.getDeductionById(id);
    }

    static async updateDeduction(id: any, payload: any) {
        return await DeductionDao.updateDeduction(id, payload);
    }

    static async deleteDeduction(id: any) {
        return await DeductionDao.deleteDeduction(id);
    }

    static async getCount(days: number){
        return await DeductionDao.getCount(days);
    }

    static async getDeductionAmount(days: number){
        return await DeductionDao.getDeductionAmount(days);
    }

    static async createSessionCancelledDeduction(therapistId: any, amount: number, deductionType: any, deductionDate: any) {
        return await DeductionDao.createSessionCancelledDeduction(
            therapistId,
            amount,
            deductionType,
            deductionDate
        )
    }

    static async getDeductionsForExport(therapistId: any, startDate: any, endDate: any){
        return await DeductionDao.getDeductionsForExport(therapistId, startDate, endDate);
    }
}