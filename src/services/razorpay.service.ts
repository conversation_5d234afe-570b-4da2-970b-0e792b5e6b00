import Razorpay from "razorpay";
import { CONFIG } from "../config/environment";
import { TransactionService } from "./transaction.service";
import { TherapistService } from "./therapist.service";
import { ClientService } from "./client.service";
import { ScheduleService } from "./schedule.service";
import { throwError } from "../util/response";
import { paymentMethod } from "../lib/enum/cashfree.enum";
import { RazorpayXDao } from "../lib/dao/razorpayx.dao";
import { RazorpayX } from "../util/razorpayx";
import TransactionModel from "../models/Transaction.model";
import crypto from "crypto";


interface IPatientDetails {
    name: string,
    email: string,
    contact: string
}


export default class RazorpayService {

    static async createPaymentLink(amount: number, therapistId: any, scheduleId: any, recurrenceDateId: any, clientId: any, isFine?: any) {
        try {


            const therapist = await TherapistService.getTherapist(therapistId);
            const client = await ClientService.getClientById(clientId);

            const schedule = await ScheduleService.getScheduleByScheduleIdTherapistAndClientId(scheduleId, therapistId, clientId);

            if (!therapist || !client || !schedule) {
                throwError("Therapist or Client or Schedule not found during creating payment link", 500);
                return false
            }

            const patient_details: IPatientDetails = {
                name: client.name,
                email: client.email,
                contact: client.phone
            }


            const exists = await TransactionService.getTransactionDetails(therapistId, scheduleId, clientId, recurrenceDateId)
            if (exists) {
                return { paymentLink: exists.paymentLink, transactionId: exists._id, paymentLinkId: exists.paymentSessionId };
            }

            // const transaction = await TransactionService.createRzpTransaction(therapistId, scheduleId, amount, clientId, recurrenceDateId);
            const transaction = new TransactionModel(
                {
                    therapistId: therapistId,
                    scheduleId: scheduleId,
                    amount: amount,
                    clientId: clientId,
                    scheduleRecId: recurrenceDateId,
                    paymentMethod: paymentMethod.RAZORPAY,
                    isFine: isFine
                });

            let options: any = {
                accept_partial: false,
                amount: amount * 100,
                currency: "INR",
                description: "Payment for session booking with " + therapist.name,
                customer: patient_details,
                notify: {
                    sms: false,
                    email: false,
                },
                reference_id: transaction._id
            };

            const rzp_instance = new Razorpay({
                key_id: CONFIG.razorpay.key_id,
                key_secret: CONFIG.razorpay.key_secret
            });
            const paymentLink = await rzp_instance.paymentLink.create(options)
            transaction.paymentLink = paymentLink.short_url;
            transaction.create_payload = paymentLink;
            transaction.paymentSessionId = paymentLink.id;
            transaction.paymentMethod = paymentMethod.RAZORPAY;

            await transaction.save();

            return { paymentLink: paymentLink.short_url, transactionId: transaction._id, paymentLinkId: paymentLink.id };
        }
        catch (error) {
            throw error;
        }
    }

    static async recreateLink(transactionId: any) {
        try {
            const transaction: any = await TransactionService.getTransactionBy_id(transactionId);
            if (!transaction) {
                throwError("no transaction found", 404);
                return false;
            }
            const rzp_instance = new Razorpay({
                key_id: CONFIG.razorpay.key_id,
                key_secret: CONFIG.razorpay.key_secret
            });

            const patient_details: IPatientDetails = {
                name: transaction.clientId.name,
                email: transaction.clientId.email,
                contact: transaction.clientId.phone
            }


            let options: any = {
                accept_partial: false,
                amount: transaction.amount * 100,
                currency: "INR",
                description: "Payment for session booking with " + transaction.therapistId.name,
                customer: patient_details,
                notify: {
                    sms: false,
                    email: false,
                },
                reference_id: transaction._id
            };


            const paymentLink = await rzp_instance.paymentLink.create(options)
            transaction.paymentLink = paymentLink.short_url;
            transaction.create_payload = paymentLink;
            transaction.paymentSessionId = paymentLink.id;
            transaction.paymentMethod = paymentMethod.RAZORPAY;

            await transaction.save();
            return { paymentLink: paymentLink.short_url, transactionId: transaction._id, paymentLinkId: paymentLink.id };
        } catch (error) {
            throw error;
        }
    }

    static async createPayout(therapistId: any, payout: any) {
        try {
            const therapist = await TherapistService.getTherapist(therapistId);
            if (!therapist) {
                return throwError("No Therpaist found", 500)
            }
            if (!therapist.bankDetails.upiId) {
                return throwError("No UPI ID found", 500)
            }
            // if(!therapist.bankDetails.upiApprove){
            //     return throwError("UPI ID not approved", 500)
            // }
            const contactDetails = {
                "name": therapist.name,
                "reference_id": String(therapist._id),
                "type": "customer",
                "email": therapist.email,
                "contact": therapist?.phone
            }

            let razorpayXContact: any = undefined;
            const existingContact = await RazorpayXDao.getRazorpayXByTherapist(therapist._id);
            if (existingContact) {
                razorpayXContact = existingContact
            }

            if (!existingContact) {
                const createContact = await RazorpayX.makeRequest(CONFIG.razorpayx.createContact, "POST", contactDetails)
                if (!createContact) {
                    return throwError("Unable to create contact from razorpayx.", 500)
                }

                const razorpayXData = {
                    therapistId: therapist._id,
                    therapistName: therapist.name,
                    therapistEmail: therapist.email,
                    contactDetails: createContact
                }

                razorpayXContact = await RazorpayXDao.create(razorpayXData)
                if (!razorpayXContact) {
                    return throwError("Unable to create razorpayx data.", 500)
                }
            }

            const fundAccountData = {
                contact_id: razorpayXContact.contactDetails.id,
                account_type: "vpa",
                vpa: {
                    address: therapist.bankDetails.upiId
                }
            }

            const fundAccount = await RazorpayX.makeRequest(CONFIG.razorpayx.createFundAccount, "POST", fundAccountData)
            if (!fundAccount) {
                return throwError("Unable to create fund account from razorpayx.", 500)
            }

            const updatedRazorpayXFundAccount: any = await RazorpayXDao.updateRazorpayXWithFundAccount(razorpayXContact._id, fundAccount)
            if (!updatedRazorpayXFundAccount) {
                return throwError("Unable to update razorpayx with fund account.", 500)
            }

            const payoutData = {
                account_number: "***************",
                fund_account_id: updatedRazorpayXFundAccount.fundAccountDetails.id,
                amount: Math.round(Number(payout.amountToBePaid) * 100),
                currency: "INR",
                mode: "UPI",
                purpose: "payout"
            }

            const createdPayout = await RazorpayX.makeRequest(CONFIG.razorpayx.createPayout, "POST", payoutData)
            if (!createdPayout) {
                return throwError("Unable to create payout from razorpayx.", 500)
            }

            const payoutDetailsData = {
                ...createdPayout,
                payout_id: payout._id
            }
            const updatedRazorpayXPayoutDetails = await RazorpayXDao.updateRazorpayXForPayout(updatedRazorpayXFundAccount._id, payoutDetailsData)
            if (!updatedRazorpayXPayoutDetails) {
                return throwError("Unable to update payoutData.", 500)
            }

            return updatedRazorpayXPayoutDetails
        }
        catch (err) {
            throw err
        }
    }

    static async createTestPayment(amountinRupees: number, therapist_id: any, upiId: any) {
            const therapist = await TherapistService.getTherapist(therapist_id);
            if (!therapist) {
                return throwError("No Therpaist found", 500)
            }
            
            const contactDetails = {
                "name": therapist.name,
                "reference_id": String(therapist._id),
                "type": "customer",
                "email": therapist.email,
                "contact": therapist?.phone
            }

            let razorpayXContact:any = await RazorpayXDao.getRazorpayXByTherapist(therapist._id);
            if(!razorpayXContact){
                const createContact = await RazorpayX.makeRequest(CONFIG.razorpayx.createContact, "POST", contactDetails)
                if (!createContact) {
                    return throwError("Unable to create contact from razorpayx.", 500)
                }
                const razorpayXData = {
                    therapistId: therapist._id,
                    therapistName: therapist.name,
                    therapistEmail: therapist.email,
                    contactDetails: createContact
                }

                razorpayXContact = await RazorpayXDao.create(razorpayXData)
                if (!razorpayXContact) {
                    return throwError("Unable to create razorpayx data.", 500)
                }
            }


            const fundAccountData = {
                contact_id: razorpayXContact.contactDetails.id,
                account_type: "vpa",
                vpa: {
                    address: upiId
                }
            }

            const fundAccount = await RazorpayX.makeRequest(CONFIG.razorpayx.createFundAccount, "POST", fundAccountData)
            if (!fundAccount) {
                return throwError("Unable to create fund account from razorpayx.", 500)
            }

            const updatedRazorpayXFundAccount: any = await RazorpayXDao.updateRazorpayXWithFundAccount(razorpayXContact._id, fundAccount)
            if (!updatedRazorpayXFundAccount) {
                return throwError("Unable to update razorpayx with fund account.", 500)
            }

            const payoutData = {
                account_number: "***************",
                fund_account_id: updatedRazorpayXFundAccount.fundAccountDetails.id,
                amount: Math.round(Number(amountinRupees) * 100),
                currency: "INR",
                mode: "UPI",
                purpose: "payout"
            }

            const createdPayout = await RazorpayX.makeRequest(CONFIG.razorpayx.createPayout, "POST", payoutData)
            if (!createdPayout) {
                return throwError("Unable to create payout from razorpayx.", 500)
            }

            return createdPayout
    }

    static async getPaymentLinkDetails(rzp_pay_id: string) {
        const rzp_instance = new Razorpay({
            key_id: CONFIG.razorpay.key_id,
            key_secret: CONFIG.razorpay.key_secret
        });
        const paymentLink = await rzp_instance.paymentLink.fetch(rzp_pay_id);
        return paymentLink;
    }

    static async cancelPaymentLink(rzp_pay_id: string) {
        const rzp_instance = new Razorpay({
            key_id: CONFIG.razorpay.key_id,
            key_secret: CONFIG.razorpay.key_secret
        });
        const paymentLink = await rzp_instance.paymentLink.cancel(rzp_pay_id);
        return paymentLink;
    }


    static async refund(paymentId: string, amountInRupees: number, therapistName: string, reason: string, deductionId: string) {
        const rzp_instance = new Razorpay({
            key_id: CONFIG.razorpay.key_id,
            key_secret: CONFIG.razorpay.key_secret
        });
        return await rzp_instance.payments.refund(paymentId, {
            "amount": amountInRupees * 100,
            "speed": "normal",
            "notes": {
                "notes_key_1": "Refund from " + therapistName,
                "notes_key_2": reason
            },
            "receipt": deductionId
        })
    }

    static async createOrder(amountInRupees: number, currency: string) {
        const rzp_instance = new Razorpay({
            key_id: CONFIG.razorpay.key_id,
            key_secret: CONFIG.razorpay.key_secret
        });

        let options = {
            amount: Number (amountInRupees) * 100,
            currency: currency,
        };
        let order;
        try {
            order = await rzp_instance.orders.create(options);
            return order;
        } catch (error) {
            throw error;
        }
    }


    static verifyPayment(paymentId: string, orderId: string, paymentSignature: string) {
        let body = orderId + "|" + paymentId;
        var expectedSignature = crypto.createHmac('sha256', CONFIG.razorpay.key_secret)
            .update(body)
            .digest('hex');

        return expectedSignature === paymentSignature;
    }

    static async fetchOrder(orderId: string) {
        const rzp_instance = new Razorpay({
            key_id: CONFIG.razorpay.key_id,
            key_secret: CONFIG.razorpay.key_secret
        });
        let order
        try {
            order = await rzp_instance.orders.fetch(orderId);
            return order;
        } catch (error) {
            throw error;
        }
    }

    static async fetchOrderPayment(orderId: string) {
        const rzp_instance = new Razorpay({
            key_id: CONFIG.razorpay.key_id,
            key_secret: CONFIG.razorpay.key_secret
        });
        let order
        try {
            order = await rzp_instance.orders.fetchPayments(orderId);
            return order;
        } catch (error) {
            throw error;
        }
    }

}