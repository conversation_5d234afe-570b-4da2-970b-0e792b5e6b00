import { S3 } from "aws-sdk";
import {
  s3DownloadParams,
  s3DownloadClient,
  getBucket,
  s3Client,
} from "../config/s3";

export class FileDownloadService {
  static async downloadFile(documentId: string) {
    try {
      let downloadparams = s3DownloadParams;
      downloadparams.Key = documentId;

      let file = await (
        await s3DownloadClient.getObject(downloadparams).promise()
      ).Body?.toString("base64");
      if (file) {
        return file;
      } else {
        return "";
      }
    } catch (err: any) {
      console.log(err.message, "downloadFile catch error");
    }
  }

  static async downloadObjectFile(documentId: string) {
    let downloadparams = s3DownloadParams;
    downloadparams.Key = documentId;

    let file = await await s3DownloadClient.getObject(downloadparams).promise();
    if (file) {
      return file;
    } else {
      return "";
    }
  }

  static async removeFiles(documentIds: string[]) {
    const objects: { Key: string }[] = documentIds.map((id) => {
      return { Key: id };
    });
    let options: S3.DeleteObjectsRequest = {
      Bucket: getBucket(),
      Delete: {
        Objects: objects,
      },
    };
    return s3Client.deleteObjects(options);
  }
}
