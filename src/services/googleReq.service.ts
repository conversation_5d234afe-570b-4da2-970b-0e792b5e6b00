import axios, { AxiosRequestConfig, Method } from "axios";
import { CONFIG } from "../config/environment";

export class GoogleRequestService {
    static async makeRequest(url: string, method: Method, access_token: any, inputPayload?: any, queryData?: any) {


        let requestConfig: AxiosRequestConfig = {
            baseURL: CONFIG.googleBaseUrl,
            url: url,
            method: method,
            headers: {
                Authorization: `Bearer ${access_token}`,
            },
        };

        if (method !== 'get' && inputPayload) {
            requestConfig.data = inputPayload;
        }

        if (queryData) {
            requestConfig.params = queryData;
        }


        return await axios.request(requestConfig).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(e => {
            console.log(e)
            return false
        });

    }

    static async refreshAccessTokenRequest(url: string, method: Method, inputPayload?: any, queryData?: any) {


        let requestConfig: AxiosRequestConfig = {
            baseURL: "https://oauth2.googleapis.com",
            url: url,
            method: method,
        };

        if (method !== 'get' && inputPayload) {
            requestConfig.data = inputPayload;
        }

        if (queryData) {
            requestConfig.params = queryData;
        }


        return await axios.request(requestConfig).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(e => {
            console.log(e)
            return false
        });

    }
}