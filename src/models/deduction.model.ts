import moment from "moment";
import { Schema, model, Document } from "mongoose";
import TherapistModel from "./Therapist.model";
import AdminModel from "./Admin.model";
import payoutsModel from "./payouts.model";
import ScheduleModel from "./Schedule.model";
import ClientModel from "./Client.model";


export enum DeductionTypeEnum {
    FINE = "FINE",
    PLATFORMFEE = "PLATFORM FEE",
    GATEWAYCHARGE = "GATEWAY CHARGE",
    TRANSFERCHARGE = "TRANSFER CHARGE",
    REFUNDTOCLIENT = "REFUND TO CLIENT",
}

export interface IDeduction extends Document {
    adminId: Schema.Types.ObjectId;
    therapistId: Schema.Types.ObjectId;
    amount: number
    deductionType: DeductionTypeEnum
    deductionDate: Date
    payoutId: Schema.Types.ObjectId
    scheduleId: Schema.Types.ObjectId
    scheduleRecId: Schema.Types.ObjectId
    clientId: Schema.Types.ObjectId
    remarks: string
    rzp_deductionId: string
}

const deductionSchema = new Schema<IDeduction>({
    adminId: {
        type: Schema.Types.ObjectId,
        ref: AdminModel
    },
    therapistId: {
        type: Schema.Types.ObjectId,
        ref: TherapistModel
    },
    amount: Number,
    deductionType: {
        type: String,
        enum: Object.values(DeductionTypeEnum)
    },
    deductionDate: {
        type: Date,
    },
    payoutId: {
        type: Schema.Types.ObjectId, 
        ref: "payout"
    },
    scheduleId: {
        type: Schema.Types.ObjectId, 
        ref: ScheduleModel
    },
    scheduleRecId: {
        type: Schema.Types.ObjectId, 
    },
    clientId: {
        type: Schema.Types.ObjectId, 
        ref: ClientModel
    },
    remarks: {
        type: String
    },
    rzp_deductionId: {
        type: String
    }
},
    {
        versionKey: false,
        timestamps: true,
        collection: "deduction"
    }
);

export default model<IDeduction>("deduction", deductionSchema)