import { Schema, model, Document } from "mongoose";
import TherapistModel, { ITherapist } from "./Therapist.model";
import ClientModel from "./Client.model";
import TransactionModel, { ITransaction } from "./Transaction.model";
import invoiceModel, { IInvoice } from "./invoice.model";

export enum ScheduleStatus {
  PENDING = "pending", // some dependency is not met before session (e.g. payment not done)
  CONFIRMED = "confirmed", // all good to go

  // Closed States
  COMPLETED = "completed",
  RESCHEDULED = "rescheduled", // rescheduled to a new date consider it as cancelled
  CANCELLED = "cancelled",
}

export enum PaymentMethodEnum {
  MANUAL = "manual", // manually by admin
  OFFLINE = "offline", // taken by therapist on his own (e.g. cash)
  ONLINE = "online", // taken online
}

export enum CalendarEnum {
  GOOGLE = "google",
  MICROSOFT = "microsoft",
  APPLE = "apple",
}

export interface IRecurrenceDates extends Document {
  fromDate: Date;
  toDate: Date;
  status: ScheduleStatus;
  paymentMethod: PaymentMethodEnum;
  syncStatus: boolean;
  cronStatus: boolean;
  transactionId: Schema.Types.ObjectId | ITransaction;
  payTrackerId: Schema.Types.ObjectId;
  other: {
    isRefunded: boolean;
    isRescheduled: boolean;
  };
  meetLink: string;
  calenderEventId: Schema.Types.ObjectId;
  amount: number;
  invoiceId: Schema.Types.ObjectId;
  payLater: boolean;
}
export interface ISchedule extends Document {
  clientId: Schema.Types.ObjectId;
  email: string;
  additionalEmails: string[];
  scheduleId: string;
  name: string;
  phone: string;
  durationOfSchedule: number;
  calendarType: CalendarEnum;
  meta: Schema.Types.Mixed;
  therapistId: Schema.Types.ObjectId | ITherapist;
  clientCountry: string;
  recurrence: string;
  isActive: boolean;
  syncWithCalender: boolean;
  tillDate: Date;
  description: string;
  recurrenceDates: IRecurrenceDates[];
  location: "online" | "offline";
  age: string;
  gender: string;
  summary: string;
  scheduleForMultiple: boolean;
  fromPublicCalender: boolean;
}

const scheduleSchema = new Schema<ISchedule>(
  {
    clientId: {
      type: Schema.Types.ObjectId,
      ref: ClientModel,
    },
    email: { type: String },
    additionalEmails: [String],
    scheduleId: { type: String, unique: true },
    name: { type: String },
    phone: { type: String },
    meta: { type: Schema.Types.Mixed },
    durationOfSchedule: { type: Number },
    calendarType: { type: String, default: CalendarEnum.GOOGLE },
    therapistId: { type: Schema.Types.ObjectId, ref: TherapistModel },
    clientCountry: { type: String },
    recurrence: { type: String },
    isActive: { type: Boolean, default: true },
    syncWithCalender: { type: Boolean, default: true },
    tillDate: { type: Date },
    description: { type: String },
    recurrenceDates: [
      {
        fromDate: { type: Date },
        toDate: { type: Date },
        status: {
          type: String,
          default: ScheduleStatus.PENDING,
          enum: Object.values(ScheduleStatus),
        },
        paymentMethod: { type: String, enum: Object.values(PaymentMethodEnum) },
        syncStatus: { type: Boolean, default: false },
        cronStatus: { type: Boolean, default: false },
        transactionId: { type: Schema.Types.ObjectId, ref: TransactionModel },
        payTrackerId: { type: Schema.Types.ObjectId, ref: "paytracker" },
        other: {
          isRefunded: { type: Boolean, default: false },
          isReScheduled: { type: Boolean, default: false },
        },
        meetLink: String,
        calenderEventId: {
          type: Schema.Types.ObjectId,
          ref: "calendarevent",
        },
        amount: { type: Number },
        invoiceId: { type: Schema.Types.ObjectId, ref: invoiceModel },
        payLater: { type: Boolean, default: false },
      },
    ],
    location: { type: String },
    age: { type: String },
    gender: { type: String },
    summary: { type: String },
    scheduleForMultiple: { type: Boolean, default: false },
    fromPublicCalender: { type: Boolean, default: false },
  },
  {
    versionKey: false,
    timestamps: true,
    collection: "schedules",
  }
);

export default model<ISchedule>("schedules", scheduleSchema);
