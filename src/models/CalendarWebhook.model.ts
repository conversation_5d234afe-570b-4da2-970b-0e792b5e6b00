/**
 * Calendar Webhook Model
 * 
 * This model stores information about Google Calendar webhook channels:
 * - Channel registration details
 * - Expiration tracking
 * - Therapist association
 * - Sync statistics
 */

import { Schema, model, Document, Model } from "mongoose";
import TherapistModel from "./Therapist.model";

export interface ICalendarWebhook extends Document {
  therapistId: Schema.Types.ObjectId;
  channelId: string;
  resourceId: string;
  resourceUri: string;
  token?: string;
  expiration: Date;
  isActive: boolean;
  createdAt: Date;
  lastNotificationAt?: Date;
  notificationCount: number;
  syncStatistics: {
    totalSyncs: number;
    successfulSyncs: number;
    failedSyncs: number;
    lastSyncAt?: Date;
    eventsProcessed: number;
    conflictsDetected: number;
    conflictsResolved: number;
  };

  // Instance methods
  isExpired(): boolean;
  isExpiringSoon(hoursThreshold?: number): boolean;
  recordNotification(): void;
  recordSync(success: boolean, eventsProcessed?: number): void;
  recordConflict(resolved?: boolean): void;
}

export interface ICalendarWebhookModel extends Model<ICalendarWebhook> {
  // Static methods
  findActiveByTherapist(therapistId: string): Promise<ICalendarWebhook[]>;
  findExpiringWebhooks(hoursThreshold?: number): Promise<ICalendarWebhook[]>;
  findByChannelId(channelId: string): Promise<ICalendarWebhook | null>;
  deactivateExpiredWebhooks(): Promise<any>;
}

const calendarWebhookSchema = new Schema<ICalendarWebhook>({
  therapistId: {
    type: Schema.Types.ObjectId,
    ref: TherapistModel,
    required: true,
    index: true
  },
  channelId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  resourceId: {
    type: String,
    required: true
  },
  resourceUri: {
    type: String,
    required: true
  },
  token: {
    type: String
  },
  expiration: {
    type: Date,
    required: true,
    index: true
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  lastNotificationAt: {
    type: Date
  },
  notificationCount: {
    type: Number,
    default: 0
  },
  syncStatistics: {
    totalSyncs: {
      type: Number,
      default: 0
    },
    successfulSyncs: {
      type: Number,
      default: 0
    },
    failedSyncs: {
      type: Number,
      default: 0
    },
    lastSyncAt: {
      type: Date
    },
    eventsProcessed: {
      type: Number,
      default: 0
    },
    conflictsDetected: {
      type: Number,
      default: 0
    },
    conflictsResolved: {
      type: Number,
      default: 0
    }
  }
}, {
  versionKey: false,
  timestamps: true,
  collection: "calendarWebhooks"
});

// Indexes for efficient querying
calendarWebhookSchema.index({ therapistId: 1, isActive: 1 });
calendarWebhookSchema.index({ expiration: 1, isActive: 1 });
calendarWebhookSchema.index({ channelId: 1 }, { unique: true });

// Methods
calendarWebhookSchema.methods.isExpired = function(): boolean {
  return new Date() >= this.expiration;
};

calendarWebhookSchema.methods.isExpiringSoon = function(hoursThreshold: number = 2): boolean {
  const thresholdTime = new Date();
  thresholdTime.setHours(thresholdTime.getHours() + hoursThreshold);
  return this.expiration <= thresholdTime;
};

calendarWebhookSchema.methods.recordNotification = function(): void {
  this.lastNotificationAt = new Date();
  this.notificationCount += 1;
};

calendarWebhookSchema.methods.recordSync = function(success: boolean, eventsProcessed: number = 0): void {
  this.syncStatistics.totalSyncs += 1;
  this.syncStatistics.lastSyncAt = new Date();
  this.syncStatistics.eventsProcessed += eventsProcessed;
  
  if (success) {
    this.syncStatistics.successfulSyncs += 1;
  } else {
    this.syncStatistics.failedSyncs += 1;
  }
};

calendarWebhookSchema.methods.recordConflict = function(resolved: boolean = false): void {
  this.syncStatistics.conflictsDetected += 1;
  if (resolved) {
    this.syncStatistics.conflictsResolved += 1;
  }
};

// Static methods
calendarWebhookSchema.statics.findActiveByTherapist = function(therapistId: string) {
  return this.find({ therapistId, isActive: true });
};

calendarWebhookSchema.statics.findExpiringWebhooks = function(hoursThreshold: number = 2) {
  const thresholdTime = new Date();
  thresholdTime.setHours(thresholdTime.getHours() + hoursThreshold);
  
  return this.find({
    isActive: true,
    expiration: { $lte: thresholdTime }
  });
};

calendarWebhookSchema.statics.findByChannelId = function(channelId: string) {
  return this.findOne({ channelId });
};

calendarWebhookSchema.statics.deactivateExpiredWebhooks = function() {
  return this.updateMany(
    {
      isActive: true,
      expiration: { $lte: new Date() }
    },
    {
      $set: { isActive: false }
    }
  );
};

export default model<ICalendarWebhook, ICalendarWebhookModel>("CalendarWebhook", calendarWebhookSchema);
