import { Schema, model, Document } from "mongoose";
import AdminModel, { IAdmin } from "./Admin.model";

export enum SubscriptionStatusEnum {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE"
}

export enum SubscriptionType {
    BASIC = "BASIC",
    TRAIL = "TRIAL PLAN"
}
export enum SubscriptionCurrencyEnum {
    INR = "INR",
    USD = "USD"
}
export interface ISubscription extends Document {
    price: number
    status: SubscriptionStatusEnum
    currency: SubscriptionCurrencyEnum
    description: string
    name: string
    subscriptionType: SubscriptionType
    validDays: number
    createdBy: Schema.Types.ObjectId | IAdmin
    isTrialIncluded: boolean;
    isAnnual: boolean;
    isMonthly: boolean;
}

const subscriptionSchema = new Schema<ISubscription>({
    price: Number,
    status: {
        type: String,
        enum: SubscriptionStatusEnum,
        default: SubscriptionStatusEnum.ACTIVE
    },
    currency: {
        type: String,
        enum: SubscriptionCurrencyEnum,
        default: SubscriptionCurrencyEnum.INR
    },
    description: {
        type: String
    },
    name: {
        type: String
    },
    isTrialIncluded: {
        type: Boolean,
        default: false
    },
    isAnnual: {
        type: Boolean,
        default: false
    },
    isMonthly: {
        type: Boolean,
        default: false
    },
    subscriptionType: {
        type: String,
        enum: Object.values(SubscriptionType),
        default: SubscriptionType.BASIC
    },
    validDays: {
        type: Number
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: AdminModel
    },
},
    {
        versionKey: false,
        timestamps: true,
        collection: "subscription"
    }
);

export default model<ISubscription>("subscription", subscriptionSchema)