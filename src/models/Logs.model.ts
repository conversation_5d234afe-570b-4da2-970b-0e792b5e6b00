import { model, Schema, Document } from "mongoose";
import TherapistModel, { ITherapist } from "./Therapist.model";


interface ILogs extends Document {
    therapist: Schema.Types.ObjectId | ITherapist,
    comments: string,
    changes: Object,
}

const logs = new Schema<ILogs>({
    therapist: { type: Schema.Types.ObjectId, ref: TherapistModel },
    comments: { type: String },
    changes: { type: Object },

}, {
    versionKey: false,
    timestamps: true,
    collection: "logs"
})

export default model<ILogs>('logs', logs);