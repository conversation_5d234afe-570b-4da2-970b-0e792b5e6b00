import { Schema, model, Document } from "mongoose";
import { paymentMethod, paymentStatus } from "../lib/enum/cashfree.enum";
import TherapistModel, { ITherapist } from "./Therapist.model";
import SubscriptionModel from "./Subscription.model";


export interface ISubscriptionTransaction extends Document {
    therapistId: Schema.Types.ObjectId | ITherapist,
    amount: number
    amountReceived: number; 
    paymentMethod: paymentMethod
    paymentDate: Date
    paymentStatus: paymentStatus
    paymentLink: string;
    paymentSessionId: string; 
    subscriptionId: Schema.Types.ObjectId
    rzpStatus: {
        create: any,
        capture: any
        orderId: string
        paymentId: string
        paymentSignature: string
    }
    invoiceNumber: {
        type: string
        unique: true
    }
}

const subscriptionTransactionSchema = new Schema<ISubscriptionTransaction>({
    therapistId: {
        type: Schema.Types.ObjectId, 
        ref: TherapistModel
    },
    amount: Number,
    amountReceived: Number,
    paymentMethod: {
        type: String, 
        enum: paymentMethod, 
        default: paymentMethod.RAZORPAY
    },
    paymentDate: Date,
    paymentStatus: {
        type: String, 
        enum: paymentStatus, 
        default: paymentStatus.PENDING
    },
    paymentLink: String, 
    paymentSessionId: String,
    subscriptionId: {
        type: Schema.Types.ObjectId, 
        ref: SubscriptionModel
    },
    rzpStatus: {
        create: {
            type: Schema.Types.Mixed
        },
        capture: {
            type: Schema.Types.Mixed
        },
        orderId: String,
        paymentId: String,
        paymentSignature: String
    },
    invoiceNumber: {
        type: String,
        unique: true,
      },
},
    {
        versionKey: false,
        timestamps: true,
        collection: "subscriptionTransaction"
    }
);

export default model<ISubscriptionTransaction>("subscriptionTransaction", subscriptionTransactionSchema)