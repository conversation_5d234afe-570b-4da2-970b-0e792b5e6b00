import { Schema, model, Document } from "mongoose";
import TherapistModel from "./Therapist.model";

export enum GenderEnum {
  MALE = "Cisgender Male",
  FEMALE = "Cisgender Female",
  TRANSGENDER = "Transgender",
  NON_BINARY = "Non-Binary",
}
export interface IClient extends Document {
  therapistId: Schema.Types.ObjectId;
  email: string;
  name: string;
  phone: string;
  gender: GenderEnum | "";
  age: string;
  password: string;
  image: string;
  clientTimeZone: string;
  address: string;
  clientId: string;
  isActive: boolean;
  defaultSessionAmount: string;
  defaultTimezone: string;
  fromPublicCalender: boolean;
}

const clientSchema = new Schema<IClient>(
  {
    therapistId: {
      type: Schema.Types.ObjectId,
      ref: TherapistModel,
    },
    email: { type: String },
    password: { type: String },
    name: { type: String },
    image: { type: String },
    phone: String,
    address: String,
    gender: {
      type: String,
      enum: [...Object.values(GenderEnum), ""], // Allow empty string
      default: "", // Optional: set empty string as default if needed
    },
    age: String,
    clientTimeZone: String,
    clientId: String,
    isActive: { type: Boolean, default: true },
    defaultSessionAmount: String,
    defaultTimezone: { type: String, default: "Asia/Kolkata" },
    fromPublicCalender: { type: Boolean, default: false },
  },
  {
    versionKey: false,
    timestamps: true,
    collection: "client",
  }
);

export default model<IClient>("client", clientSchema);
