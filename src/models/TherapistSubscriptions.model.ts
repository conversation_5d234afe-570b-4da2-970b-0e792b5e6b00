import { Schema, model, Document } from "mongoose";
import TherapistModel, { ITherapist } from "./Therapist.model";
import SubscriptionModel, { ISubscription } from "./Subscription.model";
import SubscriptionTransactionModel, { ISubscriptionTransaction } from "./SubscriptionTransaction.model";
import AdminModel, { IAdmin } from "./Admin.model";


export interface ISubcriptionTherapist extends Document {
    therapistId: Schema.Types.ObjectId | ITherapist,
    subscriptionId: Schema.Types.ObjectId | ISubscription
    subscriptionTransactionId: Schema.Types.ObjectId | ISubscriptionTransaction
    validFrom: Date,
    validTill: Date,
    id: string 
    adminId: Schema.Types.ObjectId | IAdmin
}

const subscriptionTherapistSchema = new Schema<ISubcriptionTherapist>({
    therapistId: {
        type: Schema.Types.ObjectId, 
        ref: TherapistModel
    },
    subscriptionId: {
        type: Schema.Types.ObjectId, 
        ref: SubscriptionModel
    },
    subscriptionTransactionId: {
        type: Schema.Types.ObjectId, 
        ref: SubscriptionTransactionModel
    },
    validFrom: Date,
    validTill: Date,
    id: {
        type: String,
        default: () => {
            return `ST-${Date.now()}`
        }
    },
    adminId: {
        type: Schema.Types.ObjectId,
        ref: AdminModel
    }
},
    {
        versionKey: false,
        timestamps: true,
        collection: "subscriptionTherapist"
    }
);

export default model<ISubcriptionTherapist>("subscriptionTherapist", subscriptionTherapistSchema)