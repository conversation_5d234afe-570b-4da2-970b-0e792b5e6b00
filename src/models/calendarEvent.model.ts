import { Schema, model, Document } from "mongoose";
import TherapistModel from "./Therapist.model";
import ScheduleModel from "./Schedule.model";

interface Attendee {
    email: string;
    responseStatus: string;
}

interface TimeObject {
    dateTime: string;
    timeZone: string;
}

interface CreatorOrOrganizer {
    email: string;
    self?: boolean;
}

interface Reminders {
    useDefault: boolean;
}

interface ICalendarEvent extends Document {
    therapistId: Schema.Types.ObjectId
    scheduleId: Schema.Types.ObjectId
    scheduleRecId: Schema.Types.ObjectId,
    kind?: string;
    etag: string;
    id: string;
    status: string;
    htmlLink: string;
    created: Date;
    updated: Date;
    summary: string;
    description?: string;
    location?: string;
    creator: CreatorOrOrganizer;
    organizer: CreatorOrOrganizer;
    start: TimeObject;
    end: TimeObject;
    iCalUID: string;
    sequence: number;
    attendees?: Attendee[];
    reminders: Reminders;
    eventType?: string;
    hangoutLink: string;
    conferenceData: Schema.Types.Mixed;
    visibility: string;
}


const eventSchema = new Schema<ICalendarEvent>({
    therapistId: {
        type: Schema.Types.ObjectId,
        ref: TherapistModel
    },
    scheduleId: {
        type: Schema.Types.ObjectId,
        ref: ScheduleModel
    },
    scheduleRecId: {
        type: Schema.Types.ObjectId, 
        ref: 'schedules.recurrenceDates'
    },
    kind: {
        type: String,
        default: 'calendar#event'
    },
    etag: {
        type: String,
        required: true
    },
    id: {
        type: String,
        required: true
    },
    status: {
        type: String,
        required: true,
        enum: ['confirmed', 'tentative', 'cancelled']
    },
    htmlLink: {
        type: String,
        required: true
    },
    created: {
        type: Date,
        required: true
    },
    updated: {
        type: Date,
        required: true
    },
    summary: {
        type: String,
        required: true
    },
    description: String,
    location: String,
    creator: {
        email: {
            type: String,
            required: true,
            trim: true
        },
        self: Boolean
    },
    organizer: {
        email: {
            type: String,
            required: true,
            trim: true
        },
        self: Boolean
    },
    start: {
        dateTime: {
            type: Date,
            required: true
        },
        timeZone: {
            type: String,
            required: true
        }
    },
    end: {
        dateTime: {
            type: Date,
            required: true
        },
        timeZone: {
            type: String,
            required: true
        }
    },
    iCalUID: {
        type: String,
        required: true
    },
    sequence: {
        type: Number,
        required: true
    },
    attendees: [{
        email: {
            type: String,
            required: true,
            trim: true
        },
        responseStatus: {
            type: String,
            required: true,
            enum: ['needsAction', 'declined', 'tentative', 'accepted']
        }
    }],
    reminders: {
        useDefault: {
            type: Boolean,
            default: true
        }
    },
    eventType: {
        type: String,
        default: 'default'
    },
    hangoutLink: String,
    conferenceData: {
        type: Schema.Types.Mixed,
    },
    visibility: String
},
    {
        versionKey: false,
        timestamps: true,
        collection: "calendarevent"
    }
);

export default model<ICalendarEvent>("calendarevent", eventSchema)
