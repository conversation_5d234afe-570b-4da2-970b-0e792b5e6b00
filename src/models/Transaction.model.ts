import { Schema, model, Document } from "mongoose";
import { paymentMethod, paymentStatus } from "../lib/enum/cashfree.enum";
import TherapistModel, { ITherapist } from "./Therapist.model";
import ClientModel from "./Client.model";
import AdminModel from "./Admin.model";


export interface ITransaction extends Document {
    therapistId: Schema.Types.ObjectId | ITherapist,
    scheduleId: Schema.Types.ObjectId,
    scheduleRecId: Schema.Types.ObjectId,
    amount: number
    amountReceived: number; 
    paymentMethod: paymentMethod
    paymentDate: Date
    paymentStatus: paymentStatus
    paymentDetails: Schema.Types.Mixed, 
    paymentLink: string;
    clientId: Schema.Types.ObjectId;  
    paymentSessionId: string; 
    create_payload: any
    isFine: boolean
    gatewayCharges: {
        gatewayFee: number,
        gatewayTax: number
    }
    cancelledBy: Schema.Types.ObjectId
}

const transactionSchema = new Schema<ITransaction>({
    therapistId: {
        type: Schema.Types.ObjectId, 
        ref: TherapistModel
    },
    scheduleId: {
        type: Schema.Types.ObjectId, 
        ref: 'schedules'
    },
    scheduleRecId: {
        type: Schema.Types.ObjectId, 
        ref: 'schedules.recurrenceDates'
    },
    amount: Number,
    amountReceived: Number,
    paymentMethod: {
        type: String, 
        enum: paymentMethod, 
        default: paymentMethod.RAZORPAY
    },
    paymentDate: Date,
    paymentStatus: {
        type: String, 
        enum: paymentStatus, 
        default: paymentStatus.PENDING
    },
    paymentDetails: {
        type: Schema.Types.Mixed
    },
    paymentLink: String, 
    clientId: {
        type: Schema.Types.ObjectId, 
        ref: ClientModel
    }, 
    paymentSessionId: String,
    create_payload: Schema.Types.Mixed,
    isFine: {type: Boolean},
    gatewayCharges: {
        gatewayFee: {
            type: Number, 
            default: 0
        },
        gatewayTax: {
            type: Number, 
            default: 0
        }
    },
    cancelledBy: {
        type: Schema.Types.ObjectId, 
        ref: AdminModel
    }
},
    {
        versionKey: false,
        timestamps: true,
        collection: "transaction"
    }
);

export default model<ITransaction>("transaction", transactionSchema)