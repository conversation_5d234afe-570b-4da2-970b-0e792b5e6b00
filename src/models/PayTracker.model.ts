import { Schema, model } from "mongoose";
import TherapistModel from "./Therapist.model";
import ScheduleModel from "./Schedule.model";
import ClientModel from "./Client.model";


export enum CurrencyEnum {
    INR = "INR",
    USD = "USD"
}


export enum PaymentTrackerTypeEnum {
    Advance = "Advance",
    Post_Session = "Post Session"
}


export enum PaymentTrackerStatusEnum {
    // Paid = "Paid",
    // Pending = "Pending"
    Paid_On_Time='Paid on time',
    Paid_Delayed='Paid Delay',
    Still_Pending='Still pending',
    Cancelled_Zero_Fee='Free Cancellation',
    Cancelled_Paid_Fee='Paid Cancellation',
    // Cancelled_Paid_On_Time = 'Cancelled: Paid on time',
    // Cancelled_Paid_Delayed = 'Cancelled: Paid delayed',
    // Cancelled_Still_Pending = 'Cancelled: Still pending',
}


interface IPayTracker extends Document {
    therapistId: Schema.Types.ObjectId
    scheduleId: Schema.Types.ObjectId
    scheduleRecId: Schema.Types.ObjectId
    clientId: Schema.Types.ObjectId
    dueDate: Date
    amount: {
        currency: CurrencyEnum
        value: number
    },
    paymentType: PaymentTrackerTypeEnum
    status: PaymentTrackerStatusEnum
    paymentDate: Date
    isDeleted: boolean,
    tags:[string],
    // sendRemainder:number,
    reminderCounter: {
        delayed: number,
        pending: number
        free_cancel: number,
        paid_cancel: number
    }
    isFine: boolean,
    cancellationFee: {
        currency: CurrencyEnum,
        value: number
    },
    linkExist: boolean
}

const payTrackerSchema = new Schema<IPayTracker>({
    therapistId: { type: Schema.Types.ObjectId, ref: TherapistModel },
    scheduleId: { type: Schema.Types.ObjectId, ref: ScheduleModel },
    scheduleRecId: { type: Schema.Types.ObjectId},
    clientId: { type: Schema.Types.ObjectId, ref: ClientModel },
    dueDate: { type: Date },
    amount: {
        currency: { type: String,enum:Object.values(CurrencyEnum) ,default: CurrencyEnum.INR },
        value: { type: Number }
    },
    paymentType: { type: String, enum: Object.values(PaymentTrackerTypeEnum), default: PaymentTrackerTypeEnum.Advance },
    status: { type: String, enum: Object.values(PaymentTrackerStatusEnum), default: PaymentTrackerStatusEnum.Still_Pending },
    paymentDate: { type: Date },
    isDeleted: { type: Boolean, default: false },
    tags:[{type:String}],
    // sendRemainder:{type: Number, default:0},
    reminderCounter: {
        delayed: {type: Number, default:0},
        pending: {type: Number, default:0},
        free_cancel: {type: Number, default:0},
        paid_cancel: {type: Number, default:0}
    },
    isFine:{type: Boolean, default: false},
    cancellationFee: {
        currency: { type: String, enum: Object.values(CurrencyEnum), default: CurrencyEnum.INR },
        value: { type: Number }
    },
    linkExist: {type: Boolean, default: false}
},
    {
        versionKey: false,
        timestamps: true,
        collection: "paytracker"
    }
);

export default model<IPayTracker>("paytracker", payTrackerSchema)