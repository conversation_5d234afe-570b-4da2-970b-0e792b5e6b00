import { Schema, model, Document } from "mongoose";
import TherapistModel from "./Therapist.model";
import invoiceModel, { IInvoice } from "./invoice.model";
import deductionModel from "./deduction.model";


export enum PayoutStatus {
    PENDING = "PENDING",
    PAID = "PAID",
    CANCELLED = "CANCELLED"
}

export enum PayoutMode {
    MANUAL = "MANUAL",
    CASH = "CASH",
    BANK = "BANKTRANSFER",
    CASHFREE = "CASHFREE",
    STRIPE = "STRIPE",
    UPI = "UPI",
    RZP_UPI = "razorpayx_upi"
}



export interface IPayout extends Document {
    therapistId: Schema.Types.ObjectId,

    invoices: Schema.Types.ObjectId[],
    deductions: Schema.Types.ObjectId[],

    payoutMode: PayoutMode,
    payoutStatus: PayoutStatus,

    totalAmount: number,
    commission: number,
    amountToBePaid: number,
    refId: string

    transferDate: Date,
}

const payoutSchema = new Schema<IPayout>({
    therapistId: {
        type: Schema.Types.ObjectId,
        ref: TherapistModel
    },
    invoices: [{
        type: Schema.Types.ObjectId,
        ref: invoiceModel
    }],
    deductions: [{
        type: Schema.Types.ObjectId,
        ref: deductionModel
    }],

    payoutMode: {
        type: String,
        enum: Object.values(PayoutMode)
    },
    payoutStatus: {
        type: String,
        enum: Object.values(PayoutStatus),
        default: PayoutStatus.PENDING
    },
    totalAmount: Number,
    commission: Number,
    amountToBePaid: Number,
    transferDate: Date,
    refId: {
        type: String
    }
},
    {
        versionKey: false,
        timestamps: true,
        collection: "payout"
    }
);

export default model<IPayout>("payout", payoutSchema)