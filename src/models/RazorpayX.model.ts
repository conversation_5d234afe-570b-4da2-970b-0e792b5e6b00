import { Schema, model } from "mongoose";
import TherapistModel from "./Therapist.model";

interface IRazorpayX extends Document {
    therapistId: Schema.Types.ObjectId
    therapistEmail: string
    therapistName: string
    contactDetails: Schema.Types.Mixed
    fundAccountDetails: Schema.Types.Mixed
    payoutDetails: Schema.Types.Mixed[]
}

const razorpaySchema = new Schema<IRazorpayX>({
    therapistId: { type: Schema.Types.ObjectId, ref: TherapistModel, unique: true },
    therapistEmail: String,
    therapistName: String,
    contactDetails: {type: Schema.Types.Mixed},
    fundAccountDetails: {type: Schema.Types.Mixed},
    payoutDetails: [{type: Schema.Types.Mixed}]

},
    {
        versionKey: false,
        timestamps: true,
        collection: "razorpayx"
    }
);

export default model<IRazorpayX>("razorpayx", razorpaySchema)