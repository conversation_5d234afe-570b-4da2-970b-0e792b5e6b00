import moment from "moment";
import { removeEmpty } from "../../helper/custom.helper";
import payoutsModel, { PayoutStatus } from "../../models/payouts.model";

export class PayoutDao{
    static async getAll(pageSize: any, skip: any, therapistId?: any, status?: any){
        const query = removeEmpty({therapistId: therapistId, payoutStatus: status})
        return await payoutsModel.find(query).skip(skip).limit(pageSize).populate({
            path: "therapistId",
            select: "name email"
        })
    }

    static async create(payoutData: any){
        return await payoutsModel.create(payoutData)
    }

    static async payoutId(payoutId: any){
        return await payoutsModel.findOne({_id: payoutId})
    }

    static async payoutIdPopulated(payoutId: any){
        return await payoutsModel.findOne({_id: payoutId}).populate({
            path: "therapistId",
            select: "name email bankDetails phone isVerified"
        }).populate({
            path: "invoices",
            select: "invoiceValue actualValue invoiceDate createdAt invoiceSerialNumber"
        }).populate({
            path: "deductions",
            select: "amount deductionDate deductionType"
        })
    }

    static async deletePayout(payoutId: any){
        return await payoutsModel.findOneAndDelete({_id: payoutId})
    }

    static async updatePayout(payoutId: any, payoutData: any){
        return await payoutsModel.findOneAndUpdate({_id: payoutId}, {
            $set: payoutData
        }, {new: true})
    }

    static async getCount(days: number){
        const date = moment().subtract(days, 'days').toDate();
        return await payoutsModel.countDocuments({createdAt: {$gte: date}});
    }

    static async getStatusCount(days: number, status: PayoutStatus){
        const date = moment().subtract(days, 'days').toDate();
        return await payoutsModel.countDocuments({createdAt: {$gte: date}, payoutStatus: status});
    }

    static async getPayoutAmount(days: number){
        const date = moment().subtract(days, 'days').toDate();
        const result = await payoutsModel.aggregate([
            {
                $match: {
                    createdAt: {$gte: date}
                }
            },
            {
                $group: {
                    _id: null,
                    total: {$sum: "$amountToBePaid"}
                }
            }
        ])
        return result[0].total;
    }

    static async getPendingPayouts(){
        return await payoutsModel.find().populate("therapistId")
    }

    static async getTherapistPayouts(therapistId:any, status?: PayoutStatus){
        const query = removeEmpty({therapistId: therapistId, payoutStatus: status})
        return await payoutsModel.find(query)
    }
}