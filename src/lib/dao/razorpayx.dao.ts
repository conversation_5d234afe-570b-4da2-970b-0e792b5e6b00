import RazorpayXModel from "../../models/RazorpayX.model";

export class RazorpayXDao{
    static async create(razorpayXData: any){
        return await RazorpayXModel.create(razorpayXData)
    }

    static async getRazorpayXByTherapist(therapistId: any){
        return await RazorpayXModel.findOne({therapistId: therapistId})
    }

    static async updateRazorpayXWithFundAccount(rzorpayX_id: any, fundAccount: any){
        return await RazorpayXModel.findOneAndUpdate({_id: rzorpayX_id}, {
            $set: {
                fundAccountDetails: fundAccount
            }
        }, {new: true})
    }

    static async updateRazorpayXForPayout(rzorpayX_id: any, payoutData: any){
        return await RazorpayXModel.findOneAndUpdate({_id: rzorpayX_id}, {
            $push: {
                payoutDetails: payoutData
            }
        }, {new: true})
    }
}