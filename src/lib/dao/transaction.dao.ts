import moment from "moment";
import TransactionModel from "../../models/Transaction.model";
import { paymentMethod, paymentStatus } from "../enum/cashfree.enum";
import { removeEmpty } from "../../helper/custom.helper";

export class TransactionDao {
    static async createTransaction(therapist: any, scheduleId: any, amount: number, clientId?: any, recurrenceDateId?: any) {
        return await TransactionModel.create({ therapistId: therapist._id, scheduleId: scheduleId, amount: amount, clientId: clientId, scheduleRecId: recurrenceDateId });
    }

    static async getTherapistMarkPaidOfflineTransactions(therapistId: any) {
        return await TransactionModel.find({therapistId: therapistId, paymentStatus: paymentStatus.PAID_OFFLINE});
    }

    static async getAllCount(therapistId?:any, status?: any, fromDate?: any, toDate?: any){
        const query = removeEmpty({therapistId: therapistId, paymentStatus: status})
        if(fromDate && toDate) {
            query["createdAt"] = {
                $gte: fromDate,
                $lt: toDate
            }
        }
        return await TransactionModel.find(query).count()
    }

    static async getTherapistTransactionByStatus(therapistId: any, status: any) {
        return await TransactionModel.find({ therapistId: therapistId, paymentStatus: status }).populate({path: "scheduleId", select: "recurrenceDates"})
    }

    static async getAll(pageSize: any, skip: any, therapistId?: any, status?: any, fromDate?: any, toDate?: any) {
        const query = removeEmpty({ therapistId: therapistId, paymentStatus: status })
        if (fromDate && toDate) {
            query["createdAt"] = {
                $gte: fromDate,
                $lt: toDate
            }
        }
        return await TransactionModel.find(query).skip(skip).limit(pageSize).populate({
            path: "therapistId",
            select: "name email"
        }).populate({
            path: "clientId",
            select: "clientId"
        }).populate({
            path: "scheduleId",
            select: "recurrenceDates"
        })
    }

    static async createRzpTransaction(therapistId: any, scheduleId: any, amount: number, clientId?: any, recurrenceDateId?: any) {
        return await TransactionModel.create({ therapistId: therapistId, scheduleId: scheduleId, amount: amount, clientId: clientId, scheduleRecId: recurrenceDateId, paymentMethod: paymentMethod.RAZORPAY });
    }

    static async getTransactionDetails(therapistId: any, scheduleId: any, clientId: any, recurrenceDateId: any) {
        return await TransactionModel.findOne({ therapistId: therapistId, scheduleId: scheduleId, clientId: clientId, scheduleRecId: recurrenceDateId, paymentMethod: paymentMethod.RAZORPAY, paymentStatus: paymentStatus.PENDING });

    }

    static async getTherapistFromTransaction(therapistId: any) {
        let transaction = await TransactionModel.findOne({
            therapistId: therapistId,
            customerId: { $exists: true, $ne: null }
        });

        if (!transaction) {
            return await TransactionModel.findOne({ therapistId: therapistId })
        }

        return transaction;
    }

    static async saveTransactionDetails(payload: any, transactionId: any) {
        const unixTimestamp = payload.created; // This is just an example. Replace with your Unix timestamp.
        const date = new Date(unixTimestamp * 1000);
        return await TransactionModel.findOneAndUpdate({ _id: transactionId }, {
            $set: {
                paymentDetails: payload,
                amountReceived: (payload.data.object.amount_total) / 100,
                paymentDate: date.toUTCString(),
                paymentMethod: payload.data.object.payment_method_types[0],
                paymentStatus: paymentStatus.COMPLETE
            }
        }, { new: true })
    }

    // static async getTransactionWithCustomerId(customerId: any, paymentSessionId: string) {
    // return await TransactionModel.findOne({ customerId: customerId, paymentSessionId: paymentSessionId }).sort({ createdAt: -1 });
    // }

    static async paymentLinkUpdate(transactionId: any, paymentLink: string, paymentSessionId: string) {
        return await TransactionModel.findByIdAndUpdate({ _id: transactionId }, {
            $set: {
                paymentLink: paymentLink,
                paymentSessionId: paymentSessionId
            }
        }, { new: true });
    }

    static async getTransactionById(transactionId: any) {
        return await TransactionModel.findById({ _id: transactionId }, "paymentLink")
    }

    static async getTransactionBy_Id(transactionId: any, therapistId: any) {
        return await TransactionModel.findOne({ _id: transactionId, therapistId: therapistId }).populate({ path: "clientId", select: "name email phone" }).populate({ path: "therapistId", select: "name email" })
    }

    static async getTransactionBy_id(transactionId: any) {
        return await TransactionModel.findOne({ _id: transactionId }).populate({ path: "clientId", select: "name email phone" }).populate({ path: "therapistId", select: "name email" })
    }

    static async getTherapistTransactions(therapistId: any, clientId: any) {
        const query = removeEmpty({ therapistId: therapistId, clientId: clientId })
        return await TransactionModel.find(query).populate({ path: "clientId", select: "email name clientId" })
    }

    static async getTherapistCompleteTransactions(therapistId: any) {
        return await TransactionModel.find({ therapistId: therapistId, paymentStatus: paymentStatus.COMPLETE })
    }

    static async getTherapistPendingTransactions(therapistId: any) {
        return await TransactionModel.find({ therapistId: therapistId, paymentStatus: paymentStatus.PENDING })
    }

    static async getTransactionsOfAMonth(therapistId: any, firstDayOfMonth: any, lastDayOfMonth: any) {
        return await TransactionModel.find({ therapistId: therapistId, paymentStatus: paymentStatus.COMPLETE, updatedAt: { $gte: firstDayOfMonth, $lte: lastDayOfMonth } })
    }

    static async getPendingTransactionsOfAMonth(therapistId: any, firstDayOfMonth: any, lastDayOfMonth: any) {
        // return await TransactionModel.find({therapistId: therapistId, paymentStatus: paymentStatus.PENDING, updatedAt: {$gte: firstDayOfMonth, $lte: lastDayOfMonth}})
        return await TransactionModel.find({ therapistId: therapistId, paymentStatus: paymentStatus.PENDING, updatedAt: { $lte: moment().subtract(30, "days").toDate() } })
    }

    static async getAllTransactions(therapistId: any, skip: number, pageSize: number) {
        const query = removeEmpty({ therapistId: therapistId })
        return await TransactionModel.find(query).skip(skip).limit(pageSize).populate({ path: 'therapistId', select: 'email name _id' })
    }

    static async getAllPopulatedTransactionsofTherapist(therapistId: any) {
        const query = removeEmpty({ therapistId: therapistId })
        return await TransactionModel.find(query).sort({createdAt: 1}).populate({ path: 'therapistId', select: 'email name _id' }).populate({ path: 'clientId', select: "clientId _id email" }).populate({path: 'scheduleId', select: 'recurrenceDates'})
    }

    static async getTransactionWithClientId(therapistId: any, clientId: any) {
        return await TransactionModel.find({ therapistId: therapistId, clientId: clientId }).populate({ path: "clientId", select: "name email" })
    }

    static async getPendingTransactionOfClient(clientId: any) {
        return await TransactionModel.find({ clientId: clientId, paymentStatus: paymentStatus.PENDING })
    }

    static async getTransactionByScheduleRecId(scheduleRecId: any) {
        return await TransactionModel.findOne({ scheduleRecId: scheduleRecId }, "amount")
    }
    static async getCount(days: number) {
        const date = moment().subtract(days, 'days').toDate();
        return await TransactionModel.countDocuments({ createdAt: { $gte: date } });
    }

    static async getPendingCount(days: number) {
        const date = moment().subtract(days, 'days').toDate();
        return await TransactionModel.countDocuments({ createdAt: { $gte: date }, paymentStatus: paymentStatus.PENDING });
    }

    static async getTransactionByRecId(scheduleRecId: any, therapistId: any) {
        return await TransactionModel.findOne({ therapistId: therapistId, scheduleRecId: scheduleRecId, paymentStatus: { $ne: paymentStatus.CANCELLED } })
    }

    static async getTransactionsByRecId(scheduleRecId: any, therapistId: any) {
        return await TransactionModel.find({ therapistId: therapistId, scheduleRecId: scheduleRecId, paymentStatus: { $ne: paymentStatus.CANCELLED } })
    }

    static async getTransactionByRec_id(scheduleRecId: any) {
        return await TransactionModel.find({ scheduleRecId: scheduleRecId})
    }

    static async isTransactionComplete(scheduleRecId: any, therapistId: any) {
        return await TransactionModel.findOne({ therapistId: therapistId, scheduleRecId: scheduleRecId, paymentStatus: paymentStatus.COMPLETE })
    }

    static async isPaymentPendingBefore48Hours(scheduleRecId: any, therapistId: any, twoDaysBeforeSessionStart: any) {
        return await TransactionModel.findOne({ therapistId: therapistId, scheduleRecId: scheduleRecId, paymentDate: { $lte: twoDaysBeforeSessionStart } })
    }

    static async cancelPayment(transactionId: any, therapistId: any) {
        return await TransactionModel.findOneAndUpdate({ therapistId: therapistId, _id: transactionId }, {
            $set: {
                paymentStatus: paymentStatus.CANCELLED
            }
        })
    }

    static async getLastTransactionOfClient(clientId: any, therapistId: any) {
        const query = removeEmpty({ clientId: clientId, therapistId: therapistId })
        return await TransactionModel.findOne(query).sort({ createdAt: -1 })
    }

    static async getTransactionByRecIdTherapistId(scheduleRecId: any, therpaistId: any) {
        return await TransactionModel.findOne({ therapistId: therpaistId, scheduleRecId: scheduleRecId })
    }

    static async updateTransactionByRecIdTherapistId(scheduleRecId: any, therpaistId: any, payload: any) {
        return await TransactionModel.findOneAndUpdate({ therapistId: therpaistId, scheduleRecId: scheduleRecId }, {
            $set: payload
        }, {
            new: true
        });
    }

    static async markTransactionPaidOffline(id: any) {
        return await TransactionModel.findOneAndUpdate({ _id: id }, {
            $set: {
                paymentStatus: paymentStatus.PAID_OFFLINE
            },
            $unset: {
                paymentLink: 1
            }
        })
    }

    static async getTherapistPendingTransactionsInMonth(therapistId: any, firstDayOfMonth: any, lastDayOfMonth: any) {
        return await TransactionModel.find({ therapistId: therapistId, paymentStatus: paymentStatus.PENDING, createdAt: { $gte: firstDayOfMonth, $lte: lastDayOfMonth } })
    }


    static async getPendingTransactionByRecId(scheduleRecId: any) {
        return await TransactionModel.findOne({ scheduleRecId: scheduleRecId, paymentStatus: paymentStatus.PENDING })
    }

    static async getCompletedWithDate(therapistId: any, fromDate: any, toDate: any) {
        return await TransactionModel.find({ therapistId: therapistId, paymentStatus: paymentStatus.COMPLETE, createdAt: { $gte: fromDate, $lte: toDate } })
    }

    static async getAllTransactionsByTherapist(therapistId: any) {
        return await TransactionModel.find({ therapistId: therapistId}).count()
    }

    static async getPendingWithDate(therapistId: any, fromDate: any, toDate: any) {
        return await TransactionModel.find({ therapistId: therapistId, paymentStatus: paymentStatus.PENDING, createdAt: { $gte: fromDate, $lte: toDate } }).count();
    }

    static async getOfflineWithDate(therapistId: any, fromDate: any, toDate: any) {
        return await TransactionModel.find({ therapistId: therapistId, paymentStatus: paymentStatus.PAID_OFFLINE, createdAt: { $gte: fromDate, $lte: toDate } }).count();
    }

    static async getCancelledWithDate(therapistId: any, fromDate: any, toDate: any) {
        return await TransactionModel.find({ therapistId: therapistId, paymentStatus: paymentStatus.CANCELLED, createdAt: { $gte: fromDate, $lte: toDate } }).count();
    }

    static async getFailedWithDate(therapistId: any, fromDate: any, toDate: any) {
        return await TransactionModel.find({ therapistId: therapistId, paymentStatus: paymentStatus.FAILED, createdAt: { $gte: fromDate, $lte: toDate } }).count();
    }

    static async findCompletedWithDateRange(fromDate: any, toDate: any) {
        return await TransactionModel.find({ paymentStatus: paymentStatus.COMPLETE, createdAt: { $gte: fromDate, $lte: toDate } })
    }
}