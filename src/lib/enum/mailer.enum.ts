export enum MailTypeEnum {
    SEND_OTP = 'send_otp',
    STUDENT_VERIFIED = 'student_verified',
    GENERAL_EMAIL = 'general_email',
    PASSWORD = 'password',
}

export enum BrevoMailTypeEnum {
    SIGNUP_SUCCESSFUL = "signup_successfull",
    CALENDAR_CONNECTION = "calendar_connection",
    PAYMENT_SETUP_SUCCESSFULL = "payment_setup_successfull",
    SESSION_REMINDER_THERAPIST = "session_reminder_therapist",
    MONTHLY_PAYOUT = "monthly_payout",
    PENDING_PAYMENTS = "pending_payments",
    CONFIRMED_APPOINTMENT = "confirmed_appointment",
    PENDING_APPPOINTMENT = "pending_appointment",
    PAYMENT_RECEIVER_DATA = "payment_receiver_data",
    SCHEDULE_APPOINTMENT_THERAPIST = "schedule_appointment_therapist",
    SETTING_CHANGE = "setting_change"
}