import Joi from "joi";
import { CurrencyEnum, PaymentTrackerStatusEnum, PaymentTrackerTypeEnum } from "../../models/PayTracker.model";
import { DefaultMessage } from "./message/default.message";

export const createPaytrackerSchema = Joi.object({
    scheduleId: Joi.string().required().messages(DefaultMessage.defaultRequired('scheduleId is required')),
    scheduleRecId: Joi.string().optional(),
    clientId: Joi.string().required().messages(DefaultMessage.defaultRequired('clientId is required')),
    dueDate: Joi.date().required().messages(DefaultMessage.defaultRequired('dueDate is required')),
    amount: Joi.object({
        currency: Joi.string().valid(...Object.values(CurrencyEnum)).default(CurrencyEnum.INR).messages(DefaultMessage.defaultRequired('Invalid currency value')),
        value: Joi.number().required().messages(DefaultMessage.defaultRequired('Amount value is required')),
    }).required().messages(DefaultMessage.defaultRequired('Amount is required')),
    paymentType: Joi.string().valid(...Object.values(PaymentTrackerTypeEnum)).default(PaymentTrackerTypeEnum.Advance).messages(DefaultMessage.defaultRequired('Invalid paymentType value')),
    status: Joi.string().valid(...Object.values(PaymentTrackerStatusEnum)).default(PaymentTrackerStatusEnum.Still_Pending).messages(DefaultMessage.defaultRequired('Invalid status value')),
    paymentDate: Joi.date().optional().messages(DefaultMessage.defaultRequired('Invalid date format'))
})

export const updatedPaytrackerSchema = Joi.object({
    scheduleId: Joi.string().optional(),
    scheduleRecId: Joi.string().optional(),
    clientId: Joi.string().optional(),
    dueDate: Joi.date().optional(),
    amount: Joi.object({
        currency: Joi.string().valid(...Object.values(CurrencyEnum)).default(CurrencyEnum.INR),
        value: Joi.number().optional(),
    }).optional(),
    paymentType: Joi.string().valid(...Object.values(PaymentTrackerTypeEnum)).default(PaymentTrackerTypeEnum.Advance),
    status: Joi.string().valid(...Object.values(PaymentTrackerStatusEnum)).default(PaymentTrackerStatusEnum.Still_Pending),
    paymentDate: Joi.date().optional(),
})

export const updateAmount = Joi.object({
    amount: Joi.number().required(),
})

export const chargeSession = Joi.object({
    charge: Joi.number().required().messages(DefaultMessage.defaultRequired('Charge is required'))
})