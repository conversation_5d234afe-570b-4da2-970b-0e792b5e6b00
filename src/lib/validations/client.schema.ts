import <PERSON><PERSON> from "joi";
import { DefaultMessage } from "./message/default.message";

export const ClientSchema: Jo<PERSON>.Schema = Joi.object({
  email: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Emails")),
  name: Joi.string()
    .required()
    .messages(DefaultMessage.defaultRequired("Name")),
  phone: Joi.string().optional().allow("", null),
  gender: Joi.string().optional().allow("", null),
  age: Joi.string().optional().allow("", null),
  address: Joi.string()
    .optional()
    .messages(DefaultMessage.defaultRequired("Age")),
  image: Joi.string()
    .optional()
    .messages(DefaultMessage.defaultRequired("Age")),
  defaultSessionAmount: Joi.string()
    .pattern(/^\d+(\.\d{1,2})?$/)
    .required()
    .messages({
      "string.pattern.base":
        "Default Session Amount must be a number (integer or up to 2 decimal places).",
      "any.required": "Default Session Amount is required and cannot be empty.",
      "string.empty": "Please provide a valid Default Session Amount.",
    }),

  defaultTimezone: Joi.string()
    .optional()
    .allow("", null)
    .default("Asia/Kolkata"),
});

export const MultipleClientsSchema = Joi.object({
  clients: Joi.array().items(ClientSchema).min(1).required().messages({
    "array.base": "Clients must be an array.",
    "array.min": "At least one client is required.",
    "any.required": "Clients array is required.",
  }),
});

export const clientCheckSchema = Joi.object({
  clientEmail: Joi.string().email().required().messages({
    "string.email": "Please provide a valid email address",
    "string.empty": "clientEmail is required",
    "any.required": "clientEmail is required",
    "string.base": "clientEmail must be a string",
  }),
  therapistId: Joi.string().required().messages({
    "string.empty": "Therapist ID is required",
    "any.required": "Therapist ID is required",
    "string.base": "Therapist ID must be a string",
  }),
});
