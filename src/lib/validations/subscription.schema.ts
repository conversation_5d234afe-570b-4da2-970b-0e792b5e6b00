import Joi from "joi";


import { DefaultMessage } from "./message/default.message";
import { SubscriptionType } from "../../models/Subscription.model";

export const CreateSubscripion: Joi.Schema = Joi.object({
    price: Joi.number().required().min(0).messages(DefaultMessage.defaultRequired("Price of Subscription")),
    currency: Joi.string().required().messages(DefaultMessage.defaultRequired("Currency")),
    description: Joi.string().required().messages(DefaultMessage.defaultRequired("Description")),
    name: Joi.string().required().messages(DefaultMessage.defaultRequired("Name")),
    validDays: Joi.number().required().min(1).messages(DefaultMessage.defaultRequired("Valid Days")),
    subscriptionType: Joi.string().required().valid(...Object.values(SubscriptionType)).messages(DefaultMessage.defaultRequired("Subscription Type")),
    isTrialIncluded: Joi.boolean().optional().messages(DefaultMessage.defaultRequired("Trial Inclusion Status")),
    isAnnual: Joi.boolean().optional().messages(DefaultMessage.defaultRequired("Annual Subscription Status")),
    isMonthly: Joi.boolean().optional().messages(DefaultMessage.defaultRequired("Monthly Subscription Status")),
});


export const CreateTherapistSubscripion: Joi.Schema = Joi.object({
    therapistId: Joi.string().required().messages(DefaultMessage.defaultRequired("Therapist Id")),
    subscriptionId: Joi.string().required().messages(DefaultMessage.defaultRequired("Subscription Id")),
    daysLeft: Joi.number().optional().min(0).messages(DefaultMessage.defaultRequired("Days Left")),
});
