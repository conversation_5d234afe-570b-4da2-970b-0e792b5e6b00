import express from 'express';
import { CONFIG } from '../config/environment';
import jwt from "jsonwebtoken";
import { throwError } from '../util/response';
import { TherapistService } from '../services/therapist.service';
import { AdminService } from '../services/admin.service';



export function adminAuthMiddleware() {
    return async function (req: express.Request, res: express.Response, next: express.NextFunction) {
        try {

            if (!req.headers.authorization) {
                return throwError("Invalid token", 400);
            }
            const decoded: any = jwt.verify(req.headers.authorization, CONFIG.jwt.secret);
            if (!decoded) {
                // Invalid token
                return res.status(401).send("Invalid Token")
            }
            const admin = await AdminService.getById(decoded.id);
            
            if (!admin) {
                return res.status(401).send("Invalid Credentials. Try logging in again.");
            }

            req.admin = admin;
            next();
        } catch (error) {
            next(error);
        }
    }
};
