import sgMail from "@sendgrid/mail";
import { CONFIG } from "../config/environment";
import { resetPassword } from "./emailTemplate/resetPassword";
import { MailTypeEnum } from "../lib/enum/mailer.enum";
import { throwError } from "./functions";

export class Mailer {
    static async sendEmail(mailType: MailTypeEnum, email: string, subject: string, data: any) {
        sgMail.setApiKey(CONFIG.mailApi || " ");
        let mailOptions = {
            to: email,
            from: {
                name: CONFIG.mailName || " ",
                email: CONFIG.mailFrom || " ",
            },
            subject: `${subject}` || '',
            html: Mailer.getHTML(mailType, data) || " "
        }
        return await sgMail.send(mailOptions)
    }
    static getHTML(mailType: string, data: any) {
        switch (mailType) {
            
            case MailTypeEnum.PASSWORD: 
                return resetPassword(data.name, data.email, data.password)
            default:
                throwError('Cannot find email template', 400);
        }
    }
}
