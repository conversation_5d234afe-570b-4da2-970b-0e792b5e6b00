import { CONFIG } from "../config/environment";
import { BrevoService } from "../services/brevo.service";


export class Mailer2{
    static async sendMail(senderData: any, receiverData: any, subject: any, htmlTemplate: any){
        await receiverData.map((data: any) =>  {
            return {email: data.email,
            name: data.name}
        })
        const payload = {
            sender: {
                name: senderData.name,
                email: senderData.email
            },
            to: receiverData,
            subject: subject,
            htmlContent: htmlTemplate
        }
        const data = await BrevoService.sendMail(CONFIG.brevo.sendEMail, "POST", payload)
        return data
    }
}