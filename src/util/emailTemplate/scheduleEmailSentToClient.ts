// import moment from "moment";
// import fs from "fs";

// export function scheduleEmailSentToClient(clientName: string, therapistName: string, scheduleDate: string, meetingLink: string, paymentLink: string) {
//     // return `
//     //     <h3>Hi ${clientName},</h3>
//     //     <p>Your appointment with ${therapistName} is scheduled for ${moment(scheduleDate).format('D MMM YYYY HH:mm')}.</p>
//     //     <p>You can use this link to join the call: <a href="${meetingLink}">Google Meet Link</a></p>
//     //     <p>P.S This is an automated message.</p>
//     //     <br>
//     //     <p>Click on the link below to make your payment anytime post your session!</p> 
//     //     <a href="${paymentLink}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-align: center; text-decoration: none; display: inline-block;">Pay Later</a>
//     //     <p>Thanks and Regards</p>
//     // `;
//     return `
   
// <body style="padding:0; margin:0">
//         <table style="width:100%; background-color: #FEE2D6; font-family:Arial, sans-serif;line-height: 1.5; color:#222222;" align="center" cellspacing="0" cellpadding="0" border="0">
//             <tr>
//                 <td>
//                     <table style="width:600px; background-color: #fff;" align="center" cellspacing="0" cellpadding="0" border="0">
//                         <tr>
//                             <td bgcolor="white" style="padding:30px;">
//                                 <table style="width:100%; background-color: #fff;" align="center" cellspacing="0" cellpadding="0" border="0">
//                                     <tr>
//                                         <td>
//                                             <div style="display: flex; flex-direction: row; gap: 10px; align-items: center;">
//                                                 <img src="https://thoughtpudding-public.s3.ap-south-1.amazonaws.com/logo.png" alt="Thought Pudding" srcset="" height="50px">
//                                                 <p style="font-size: x-large; font-weight: 500;">Thought Pudding</p>
//                                             </div>
//                                         </td>
//                                     </tr>
//                                     <tr>
//                                         <td style="font-family:Arial, sans-serif;line-height: 1.5;">
//                                             <p>
//                                                 Hi ${clientName},
//                                             </p>
//                                             <p>
//                                                 Your appointment with ${therapistName} is scheduled <br>
//                                                 for ${moment(scheduleDate).format('MMMM DD, YYYY hh:mm A')}.
//                                             </p>
//                                             <p>
//                                                 You can use this link to join the call: <a href="${meetingLink}">Google Meet Link</a>
//                                             </p>
//                                             <p style="font-size: 12px; padding-bottom: 16px;">
//                                                 <span style="font-size: 11px;">P.S.</span> This is an automated message.
//                                             </p>
//                                                                                         <p>
//                                                 Click on the link below to make your payment!
//                                             </p>

//                                         </td>
//                                     </tr>
//                                     <tr>
//                                         <td align="center" style="padding-top: 20px;">
//                                             <table style="width:auto; background-color: #9FDEA5;" align="center" cellspacing="0" cellpadding="0" border="0">
//                                                 <tr>
//                                                     <td style="background-color: #9FDEA5;">
//                                                         <a href="${paymentLink}" target="_blank" rel="noopener noreferrer" style="font-family:Arial, sans-serif;line-height: 1.5; color:#222222; text-decoration: none;padding:12px 24px; background-color: #9FDEA5; border-radius: 10px;">Pay Now</a>
//                                                     </td>
//                                                 </tr>
//                                             </table>
//                                         </td>
//                                     </tr>
//                                 </table>
//                             </td>
//                         </tr>
//                         <tr>
//                             <td style="border-top:1px solid #eee; padding-top: 20px; padding-bottom: 0px;">
//                                 <table style="width:auto;" align="center" cellspacing="0" cellpadding="0" border="0">
//                                     <tr>
//                                         <td>
//                                             <a href="http://" target="_blank" rel="noopener noreferrer" style="font-family:Arial, sans-serif;line-height: 1.5; color:#222222; text-decoration: none;">
//                                                 <img src="https://panicle-public.s3.ap-south-1.amazonaws.com/facebook.png" alt="" srcset="">
//                                             </a>
                                            
//                                         </td>
//                                         <td width="10">

//                                         </td>
//                                         <td>
//                                             <a href="http://" target="_blank" rel="noopener noreferrer" style="font-family:Arial, sans-serif;line-height: 1.5; color:#222222; text-decoration: none;">
//                                                 <img src="https://panicle-public.s3.ap-south-1.amazonaws.com/twitter.png" alt="" srcset="">
//                                             </a>
                                            
//                                         </td>
//                                         <td width="10">

//                                         </td>

//                                         <td>
//                                             <a href="http://" target="_blank" rel="noopener noreferrer" style="font-family:Arial, sans-serif;line-height: 1.5; color:#222222; text-decoration: none;">
//                                                 <img src="https://panicle-public.s3.ap-south-1.amazonaws.com/linkedin.png" alt="" srcset="">
//                                             </a>
                                            
//                                         </td>
//                                         <td width="10">

//                                         </td>


//                                     </tr>

//                             </td>
                            
//                         </tr>
                        
                        
//                     </table>
//                 </td>
//             </tr>
//             <tr>
//                 <td bgcolor="white" style="font-family:Arial, sans-serif;line-height: 1.5; text-align: center; padding-bottom: 30px">
//                  Copyright ©Thought Pudding 2024<br>
//                  All rights reserved.
//                 </td>
//              </tr>
//              <tr>
//                 <!-- <td bgcolor="#FEE2D6" style="padding-top:20px; padding-bottom:20px;font-family:Arial, sans-serif;line-height: 1.5; text-align: center; font-size: 13px; color: #222;">
//                     If you'd like to unsubscribe and stop receiving these emails <a href="" target="_blank" style="color:#222">click here</a> .
//                 </td> -->
//              </tr>
//         </table>
// </body>
// `
// }

// export function scheduleEmailSentToOfflineClient(clientName: string, therapistName: string, scheduleDate: string, paymentLink: string) {
//     // return `
//     //     <h3>Hi ${clientName},</h3>
//     //     <p>Your appointment with ${therapistName} is scheduled for ${moment(scheduleDate).format('D MMM YYYY HH:mm')}.</p>
//     //     <p>You can use this link to join the call: <a href="${meetingLink}">Google Meet Link</a></p>
//     //     <p>P.S This is an automated message.</p>
//     //     <br>
//     //     <p>Click on the link below to make your payment anytime post your session!</p> 
//     //     <a href="${paymentLink}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-align: center; text-decoration: none; display: inline-block;">Pay Later</a>
//     //     <p>Thanks and Regards</p>
//     // `;
//     return `
   
// <body style="padding:0; margin:0">
//         <table style="width:100%; background-color: #FEE2D6; font-family:Arial, sans-serif;line-height: 1.5; color:#222222;" align="center" cellspacing="0" cellpadding="0" border="0">
//             <tr>
//                 <td>
//                     <table style="width:600px; background-color: #fff;" align="center" cellspacing="0" cellpadding="0" border="0">
//                         <tr>
//                             <td bgcolor="white" style="padding:30px;">
//                                 <table style="width:100%; background-color: #fff;" align="center" cellspacing="0" cellpadding="0" border="0">
//                                     <tr>
//                                         <td>
//                                             <div style="display: flex; flex-direction: row; gap: 10px; align-items: center;">
//                                                 <img src="https://thoughtpudding-public.s3.ap-south-1.amazonaws.com/logo.png" alt="Thought Pudding" srcset="" height="50px">
//                                                 <p style="font-size: x-large; font-weight: 500;">Thought Pudding</p>
//                                             </div>
//                                         </td>
//                                     </tr>
//                                     <tr>
//                                         <td style="font-family:Arial, sans-serif;line-height: 1.5;">
//                                             <p>
//                                                 Hi ${clientName},
//                                             </p>
//                                             <p>
//                                                 Your appointment with ${therapistName} is scheduled <br>
//                                                 for ${moment(scheduleDate).format('MMMM DD, YYYY hh:mm A')} (in-person).
//                                             </p>
//                                             <p style="font-size: 12px; padding-bottom: 16px;">
//                                                 <span style="font-size: 11px;">P.S.</span> This is an automated message.
//                                             </p>
//                                                                                         <p>
//                                                 Click on the link below to make your payment!
//                                             </p>

//                                         </td>
//                                     </tr>
//                                     <tr>
//                                         <td align="center" style="padding-top: 20px;">
//                                             <table style="width:auto; background-color: #9FDEA5;" align="center" cellspacing="0" cellpadding="0" border="0">
//                                                 <tr>
//                                                     <td style="background-color: #9FDEA5;">
//                                                         <a href="${paymentLink}" target="_blank" rel="noopener noreferrer" style="font-family:Arial, sans-serif;line-height: 1.5; color:#222222; text-decoration: none;padding:12px 24px; background-color: #9FDEA5; border-radius: 10px;">Pay Now</a>
//                                                     </td>
//                                                 </tr>
//                                             </table>
//                                         </td>
//                                     </tr>
//                                 </table>
//                             </td>
//                         </tr>
//                         <tr>
//                             <td style="border-top:1px solid #eee; padding-top: 20px; padding-bottom: 0px;">
//                                 <table style="width:auto;" align="center" cellspacing="0" cellpadding="0" border="0">
//                                     <tr>
//                                         <td>
//                                             <a href="http://" target="_blank" rel="noopener noreferrer" style="font-family:Arial, sans-serif;line-height: 1.5; color:#222222; text-decoration: none;">
//                                                 <img src="https://panicle-public.s3.ap-south-1.amazonaws.com/facebook.png" alt="" srcset="">
//                                             </a>
                                            
//                                         </td>
//                                         <td width="10">

//                                         </td>
//                                         <td>
//                                             <a href="http://" target="_blank" rel="noopener noreferrer" style="font-family:Arial, sans-serif;line-height: 1.5; color:#222222; text-decoration: none;">
//                                                 <img src="https://panicle-public.s3.ap-south-1.amazonaws.com/twitter.png" alt="" srcset="">
//                                             </a>
                                            
//                                         </td>
//                                         <td width="10">

//                                         </td>

//                                         <td>
//                                             <a href="http://" target="_blank" rel="noopener noreferrer" style="font-family:Arial, sans-serif;line-height: 1.5; color:#222222; text-decoration: none;">
//                                                 <img src="https://panicle-public.s3.ap-south-1.amazonaws.com/linkedin.png" alt="" srcset="">
//                                             </a>
                                            
//                                         </td>
//                                         <td width="10">

//                                         </td>


//                                     </tr>

//                             </td>
                            
//                         </tr>
                        
                        
//                     </table>
//                 </td>
//             </tr>
//             <tr>
//                 <td bgcolor="white" style="font-family:Arial, sans-serif;line-height: 1.5; text-align: center; padding-bottom: 30px">
//                  Copyright ©Thought Pudding 2024<br>
//                  All rights reserved.
//                 </td>
//              </tr>
//              <tr>
//                 <!-- <td bgcolor="#FEE2D6" style="padding-top:20px; padding-bottom:20px;font-family:Arial, sans-serif;line-height: 1.5; text-align: center; font-size: 13px; color: #222;">
//                     If you'd like to unsubscribe and stop receiving these emails <a href="" target="_blank" style="color:#222">click here</a> .
//                 </td> -->
//              </tr>
//         </table>
// </body>
// `
// }

// export function mailWithoutPayLinkToClient(clientName: string, therapistName: string, scheduleDate: string, meetingLink: any) {
//     // return `
//     //     <h3>Hi ${clientName},</h3>
//     //     <p>Your appointment with ${therapistName} is scheduled for ${moment(scheduleDate).format('D MMM YYYY HH:mm')}.</p>
//     //     <p>You can use this link to join the call: <a href="${meetingLink}">Google Meet Link</a></p>
//     //     <p>P.S This is an automated message.</p>
//     //     <br>
//     //     <p>Click on the link below to make your payment anytime post your session!</p> 
//     //     <a href="${paymentLink}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-align: center; text-decoration: none; display: inline-block;">Pay Later</a>
//     //     <p>Thanks and Regards</p>
//     // `;
//     return `
   
// <body style="padding:0; margin:0">
// <table style="width:100%; background-color: #FEE2D6; font-family:Arial, sans-serif;line-height: 1.5; color:#222222;" align="center" cellspacing="0" cellpadding="0" border="0">
//     <tr>
//         <td>
//             <table style="width:600px; background-color: #fff;" align="center" cellspacing="0" cellpadding="0" border="0">
//                 <tr>
//                     <td bgcolor="white" style="padding:30px;">
//                         <table style="width:100%; background-color: #fff;" align="center" cellspacing="0" cellpadding="0" border="0">
//                             <tr>
//                                 <td>
//                                     <div style="display: flex; flex-direction: row; gap: 10px; align-items: center;">
//                                         <img src="https://thoughtpudding-public.s3.ap-south-1.amazonaws.com/logo.png" alt="Thought Pudding" srcset="" height="50px">
//                                         <p style="font-size: 19px; font-weight: 900;">Thought Pudding</p>
//                                     </div>
//                                 </td>
//                             </tr>
//                             <tr>
//                                 <td style="font-family:Arial, sans-serif;line-height: 1.5;">
//                                     <p>
//                                         Hi${clientName ? " " + clientName + "," : ","}
//                                     </p>
//                                     <p>
//                                         Your appointment with ${therapistName} is scheduled <br/>
//                                         for ${moment(scheduleDate).format('MMMM DD, YYYY hh:mm a')} (in-person).
//                                     </p>
//                                     ${meetingLink && `<p> You can use this link to join the call: <a href="${meetingLink}">Google Meet Link</a>
//                                     </p>` }
//                                     <p style="font-size: 12px; padding-bottom: 16px;">
//                                         <span style="font-size: 11px;">P.S.</span> This is an automated message.
//                                     </p>

//                                 </td>
//                             </tr>
//                         </table>
//                     </td>
//                 </tr>
//                 <tr>
//                     <td style="border-top:1px solid #eee; padding-top: 20px; padding-bottom: 0px;">
//                         <table style="width:auto;" align="center" cellspacing="0" cellpadding="0" border="0">
//                             <tr>
//                                 <td>
//                                     <a href="http://" target="_blank" rel="noopener noreferrer" style="font-family:Arial, sans-serif;line-height: 1.5; color:#222222; text-decoration: none;">
//                                         <img src="https://panicle-public.s3.ap-south-1.amazonaws.com/facebook.png" alt="" srcset="">
//                                     </a>
                                    
//                                 </td>
//                                 <td width="10">

//                                 </td>
//                                 <td>
//                                     <a href="http://" target="_blank" rel="noopener noreferrer" style="font-family:Arial, sans-serif;line-height: 1.5; color:#222222; text-decoration: none;">
//                                         <img src="https://panicle-public.s3.ap-south-1.amazonaws.com/twitter.png" alt="" srcset="">
//                                     </a>
                                    
//                                 </td>
//                                 <td width="10">

//                                 </td>

//                                 <td>
//                                     <a href="http://" target="_blank" rel="noopener noreferrer" style="font-family:Arial, sans-serif;line-height: 1.5; color:#222222; text-decoration: none;">
//                                         <img src="https://panicle-public.s3.ap-south-1.amazonaws.com/linkedin.png" alt="" srcset="">
//                                     </a>
                                    
//                                 </td>
//                                 <td width="10">

//                                 </td>


//                             </tr>

//                     </td>
                    
//                 </tr>
                
                
//             </table>
//         </td>
//     </tr>
//     <tr>
//         <td bgcolor="white" style="font-family:Arial, sans-serif;line-height: 1.5; text-align: center; padding-bottom: 30px">
//          © Thought Pudding 2024<br>
//          All rights reserved.
//           <a href="https://thoughtpudding.com"> Thought Pudding </a>
//       </td>
//      </tr>
//      <tr>
//         <!-- <td bgcolor="#FEE2D6" style="padding-top:20px; padding-bottom:20px;font-family:Arial, sans-serif;line-height: 1.5; text-align: center; font-size: 13px; color: #222;">
//             If you'd like to unsubscribe and stop receiving these emails <a href="" target="_blank" style="color:#222">click here</a> .
//         </td> -->
//      </tr>
// </table>
// </body>
// `
// }

// export function scheduleEmailWhenIsBeforeFalse(clientName: string, therapistName: string, scheduleDate: string, meetingLink: string, paymentLink: string) {
//     // return `
//     //     <h3>Hi ${clientName},</h3>
//     //     <p>Your appointment with ${therapistName} is scheduled for ${moment(scheduleDate).format('D MMM YYYY HH:mm')}.</p>
//     //     <p>You can use this link to join the call: <a href="${meetingLink}">Google Meet Link</a></p>
//     //     <p>P.S This is an automated message.</p>
//     //     <br>
//     //     <p>Click on the link below to make your payment anytime post your session!</p> 
//     //     <a href="${paymentLink}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-align: center; text-decoration: none; display: inline-block;">Pay Later</a>
//     //     <p>Thanks and Regards</p>
//     // `;
//     return `
   
// <body style="padding:0; margin:0">
//         <table style="width:100%; background-color: #FEE2D6; font-family:Arial, sans-serif;line-height: 1.5; color:#222222;" align="center" cellspacing="0" cellpadding="0" border="0">
//             <tr>
//                 <td>
//                     <table style="width:600px; background-color: #fff;" align="center" cellspacing="0" cellpadding="0" border="0">
//                         <tr>
//                             <td bgcolor="white" style="padding:30px;">
//                                 <table style="width:100%; background-color: #fff;" align="center" cellspacing="0" cellpadding="0" border="0">
//                                     <tr>
//                                         <td>
//                                             <div style="display: flex; flex-direction: row; gap: 10px; align-items: center;">
//                                                 <img src="https://thoughtpudding-public.s3.ap-south-1.amazonaws.com/logo.png" alt="Thought Pudding" srcset="" height="50px">
//                                                 <p style="font-size: x-large; font-weight: 500;">Thought Pudding</p>
//                                             </div>
//                                         </td>
//                                     </tr>
//                                     <tr>
//                                         <td style="font-family:Arial, sans-serif;line-height: 1.5;">
//                                             <p>
//                                                 Hi ${clientName},
//                                             </p>
//                                             <p>
//                                                 Your appointment with ${therapistName} is scheduled <br>
//                                                 for ${moment(scheduleDate).format('MMMM DD, YYYY hh:mm A')}.
//                                             </p>
//                                             <p>
//                                                 You can use this link to join the call: <a href="${meetingLink}">Google Meet Link</a>
//                                             </p>
//                                             <p style="font-size: 12px; padding-bottom: 16px;">
//                                                 <span style="font-size: 11px;">P.S.</span> This is an automated message.
//                                             </p>
//                                                                                         <p>
//                                                 Click on the link below to make your payment!
//                                             </p>

//                                         </td>
//                                     </tr>
//                                     <tr>
//                                         <td align="center" style="padding-top: 20px;">
//                                             <table style="width:auto; background-color: #9FDEA5;" align="center" cellspacing="0" cellpadding="0" border="0">
//                                                 <tr>
//                                                     <td style="background-color: #9FDEA5;">
//                                                         <a href="${paymentLink}" target="_blank" rel="noopener noreferrer" style="font-family:Arial, sans-serif;line-height: 1.5; color:#222222; text-decoration: none;padding:12px 24px; background-color: #9FDEA5; border-radius: 10px;">Pay Later</a>
//                                                     </td>
//                                                 </tr>
//                                             </table>
//                                         </td>
//                                     </tr>
//                                 </table>
//                             </td>
//                         </tr>
//                         <tr>
//                             <td style="border-top:1px solid #eee; padding-top: 20px; padding-bottom: 0px;">
//                                 <table style="width:auto;" align="center" cellspacing="0" cellpadding="0" border="0">
//                                     <tr>
//                                         <td>
//                                             <a href="http://" target="_blank" rel="noopener noreferrer" style="font-family:Arial, sans-serif;line-height: 1.5; color:#222222; text-decoration: none;">
//                                                 <img src="https://panicle-public.s3.ap-south-1.amazonaws.com/facebook.png" alt="" srcset="">
//                                             </a>
                                            
//                                         </td>
//                                         <td width="10">

//                                         </td>
//                                         <td>
//                                             <a href="http://" target="_blank" rel="noopener noreferrer" style="font-family:Arial, sans-serif;line-height: 1.5; color:#222222; text-decoration: none;">
//                                                 <img src="https://panicle-public.s3.ap-south-1.amazonaws.com/twitter.png" alt="" srcset="">
//                                             </a>
                                            
//                                         </td>
//                                         <td width="10">

//                                         </td>

//                                         <td>
//                                             <a href="http://" target="_blank" rel="noopener noreferrer" style="font-family:Arial, sans-serif;line-height: 1.5; color:#222222; text-decoration: none;">
//                                                 <img src="https://panicle-public.s3.ap-south-1.amazonaws.com/linkedin.png" alt="" srcset="">
//                                             </a>
                                            
//                                         </td>
//                                         <td width="10">

//                                         </td>


//                                     </tr>

//                             </td>
                            
//                         </tr>
                        
                        
//                     </table>
//                 </td>
//             </tr>
//         </table>
// </body>
// `
// }