import <PERSON><PERSON>pay from "razorpay";
import { CONFIG } from "../config/environment";

export class RazorpayUtility {
  static async createPaymentLink(amount: number, therapist: any, referenceId?: any) {
    const instance = new Razorpay({ key_id: CONFIG.razorpay.key_id, key_secret: CONFIG.razorpay.key_secret });
    console.log(amount);
    amount = amount * 100
    const paymentLink = await instance.paymentLink.create({
      upi_link: true,
      amount: amount,
      currency: "INR",
      accept_partial: false,
      first_min_partial_amount: 100,
      // description: paymentLinkData.description,
      customer: {
        name: therapist.name,
        email: therapist.email
      },
      notify: {
        sms: true,
        email: true
      },
      reminder_enable: true,
      reference_id: referenceId
    })
    return await paymentLink
  }
}