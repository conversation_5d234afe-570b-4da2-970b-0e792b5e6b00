import { CalendarCron } from "./calendar.cron";
import { MarkSchedueCron } from "./markScheduleCompleted.cron";
import { SendEmailCron } from "./sendEmail.cron";
import { OptimizedCalendarCron } from "./optimizedCalendar.cron";

export class Cron{
    // static start(){
    //     console.log('\x1b[36m%s\x1b[0m', "Syncing Calendars");
        // CalendarCron.syncCalendar()
    // }

    static sendEmail(){
        console.log('\x1b[36m%s\x1b[0m', "Sending Email");
        SendEmailCron.sendEmail2()
    }

    static markCompleted(){
        console.log('\x1b[36m%s\x1b[0m', "Marking Complete");
        MarkSchedueCron.markScheduleCompleted()
        MarkSchedueCron.markScheduleCancelled()
    }

    static async initializeOptimizedCalendar(){
        console.log('\x1b[36m%s\x1b[0m', "Initializing Optimized Calendar Crons");
        await OptimizedCalendarCron.initializeCronJobs()
    }

}