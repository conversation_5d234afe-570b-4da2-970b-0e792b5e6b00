import cron from "node-cron";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { TherapistDao } from "../lib/dao/therapist.dao";
import {
  GoogleCalendarService,
  IAddEventPayload,
} from "../services/googleCalendar.service";
import moment from "moment";
import { ClientDao } from "../lib/dao/client.dao";
import { ScheduleService } from "../services/schedule.service";
// import { scheduleEmailSentToClient } from '../util/emailTemplate/scheduleEmailSentToClient';
import { Mailer2 } from "../util/mailer2";
import RazorpayService from "../services/razorpay.service";
import createScheduleTemplate from "../util/emailTemplate/create.schedule";
import { PayTrackerController } from "../controller/payTracker.controller";
import PayTrackerModel, {
  PaymentTrackerStatusEnum,
  PaymentTrackerTypeEnum,
} from "../models/PayTracker.model";
import { PayTrackerService } from "../services/payTracker.service";
import scheduleReminder from "../util/emailTemplate/scheduleReminder";
import { MailSubjectEnum } from "../lib/enum/subject.enum";

export class SendEmailCron {
  static async sendEmail2() {
    cron.schedule("*/30 * * * *", async () => {
      // console.log(
      //   "Starting Cron Job " + moment().format("DD MMM YYYY HH:mm:ss")
      // );
      const therapists = await TherapistDao.getAllActiveTherapists();
      // console.log("Total therapists: ", therapists.length);
      for (let therapist of therapists) {
        try {
          // console.log("Working on therapist: ", therapist.name);
          const currentDate = moment();
          const tillDate = moment().add(1, "week");
          const schedules = await ScheduleDao.getTherapistSchedulesInDateRange(
            therapist._id,
            currentDate,
            tillDate
          );
          for (const schedule of schedules) {
            try {
              // console.log("Working on schedule: ", schedule._id);
              const client = await ClientDao.findClientById(schedule.clientId);
              if (!client) {
                continue;
              }

              let emails: any = [];
              emails.push(therapist.email);
              emails.push(client.email);

              let senderData = {
                email: therapist.email,
                name: therapist.name,
              };
              const receiverData = [
                {
                  email: client?.email,
                  name: client?.name || "There",
                },
              ];

              for (const reccurance of schedule.recurrenceDates) {
                try {
                  // console.log("Working on reccurance: ", reccurance._id);

                  if (
                    moment(reccurance.fromDate).isAfter(currentDate) &&
                    moment(reccurance.fromDate).isBefore(tillDate)
                  ) {
                    if (reccurance.cronStatus == true) {
                      continue;
                    }
                    // console.log(
                    //   "Recurance ",
                    //   reccurance,
                    //   " is within the range"
                    // );

                    const addEventPayload: IAddEventPayload = {
                      emails: emails,
                      summary: schedule?.summary,
                      location: schedule?.location,
                      description: schedule?.description,
                    };

                    const recurranceData = {
                      fromDate: reccurance.fromDate,
                      toDate: reccurance.toDate,
                      _id: reccurance._id,
                    };

                    let googleCalenderEvent = {
                      link: reccurance.meetLink,
                      calenderEventId: reccurance.calenderEventId,
                    };

                    if (!reccurance.calenderEventId) {
                      googleCalenderEvent =
                        await GoogleCalendarService.addSingleEventToCalender(
                          therapist._id,
                          addEventPayload,
                          recurranceData,
                          schedule._id
                        );
                    }
                    // console.log(
                    //   "Recurance ",
                    //   reccurance._id,
                    //   " created GCal Event",
                    //   googleCalenderEvent.calenderEventId
                    // );

                    let paymentLink = undefined;
                    // if(therapist.menus.paymentGateway){
                    //     if (reccurance.amount > 0) {
                    //         paymentLink = await RazorpayService.createPaymentLink(reccurance?.amount, therapist._id, schedule._id, reccurance._id, client._id);
                    //         console.log("Recurance ", reccurance._id, " created Payment Link")
                    //     }
                    // }

                    let payTrackerId = undefined;
                    let payTracker = undefined;

                    if (therapist.menus.paymentTracker) {
                      let amount = 0;
                      if (reccurance.amount) {
                        amount = reccurance.amount || 0;
                      }
                      if (amount == 0) {
                        amount = client.defaultSessionAmount
                          ? Number(client.defaultSessionAmount)
                          : 0;
                      }

                      const paymentWhen = reccurance.payLater
                        ? PaymentTrackerTypeEnum.Post_Session
                        : PaymentTrackerTypeEnum.Advance;
                      const payload = {
                        therapistId: therapist._id,
                        scheduleId: schedule._id,
                        scheduleRecId: reccurance._id,
                        clientId: client._id,
                        dueDate: reccurance.fromDate,
                        amount: {
                          currency: "INR",
                          value: amount,
                        },
                        paymentType: paymentWhen,
                        status: PaymentTrackerStatusEnum.Still_Pending,
                        paymentDate: undefined,
                        isDeleted: false,
                        tags: [],
                        sendRemainder: 0,
                        isFine: false,
                        cancellationFee: {
                          currency: "INR",
                          value: 0,
                        },
                      };

                      payTracker = await PayTrackerModel.findOne({
                        scheduleRecId: reccurance._id,
                      });
                      if (payTracker) {
                        payTrackerId = payTracker._id;
                      } else {
                        payTracker = await PayTrackerService.createPayTracker(
                          payload
                        );
                        payTrackerId = payTracker._id;
                      }

                      await ScheduleDao.updateRecurrenceAmountForPayTracker(
                        therapist._id,
                        schedule._id,
                        reccurance._id,
                        amount,
                        payTrackerId
                      );
                    }

                    await ScheduleService.updateRecurrenceDate(
                      schedule._id,
                      reccurance._id,
                      // (paymentLink == true || paymentLink == false || paymentLink == undefined) ? undefined : paymentLink.transactionId,
                      undefined,
                      googleCalenderEvent.link,
                      googleCalenderEvent.calenderEventId,
                      payTrackerId
                    );

                    // let subject = schedule.summary;
                    let subject = MailSubjectEnum.REMAINDER;
                    let htmlTemplate = scheduleReminder({
                      clientName: client.name || "There",
                      therapistName: therapist.name,
                      scheduleDate: reccurance?.fromDate,
                      meetingLink:
                        schedule.location == "online"
                          ? googleCalenderEvent.link
                          : undefined,
                      // paymentLink: undefined,
                      // (paymentLink == true || paymentLink == false || paymentLink == undefined) ? undefined : paymentLink.paymentLink,
                      // payLater: reccurance.payLater,
                      amount: reccurance.amount,
                    });
                    const sentEmail = await Mailer2.sendMail(
                      senderData,
                      receiverData,
                      subject,
                      htmlTemplate
                    );
                    // console.log(
                    //   "Completed Working on reccurance: ",
                    //   reccurance._id
                    // );
                  }
                } catch (error) {
                  console.error(
                    "Error processing reccurance " +
                      reccurance._id +
                      ": " +
                      error
                  );
                }
              }
            } catch (error) {
              console.error(
                "Error processing schedule " + schedule._id + ": " + error
              );
            }
          }
          console.log("Completed Working on therapist: ", therapist.name);
        } catch (error) {
          console.error(
            "Error processing therapist " + therapist.name + ": " + error
          );
        }
      }

      // console.log(
      //   "Completed Cron Job " + moment().format("DD MMM YYYY HH:mm:ss")
      // );
    });
  }

  // static async sendEmail() {
  //     cron.schedule('2 * * * *', async () => {
  //         console.log("Starting Cron Job " + moment().format("DD MMM YYYY HH:mm:ss"));
  //         const therapists: any = await TherapistDao.getAllTherapists();
  //         console.log("Total therapists: ", therapists.length)
  //         for (let therapist of therapists) {
  //             console.log("Therapist: ", therapist.name)
  //             const schedules = await ScheduleDao.getAllRecurrenceDatesForCron(therapist._id);
  //             for (let schedule of schedules) {
  //                 const oneWeekAfterCurrentTime = moment().add(1, "week");
  //                 let recurrenceDatesWithinWeek: any = await schedule.recurrenceDates.filter((recurrence) => moment(recurrence.fromDate).isBefore(oneWeekAfterCurrentTime) && !recurrence.syncStatus && !recurrence.cronStatus);
  //                 const datesWithInAWeek = recurrenceDatesWithinWeek
  //                 let emails: any = [];
  //                 let senderData = {
  //                     email: therapist.email,
  //                     name: therapist.name
  //                 }
  //                 const client: any = await ClientDao.findClientById(schedule?.clientId)

  //                 await emails.push(therapist?.email);
  //                 await emails.push(client?.email);
  //                 const receiverData = [{
  //                     email: client?.email,
  //                     name: client?.name
  //                 }]
  //                 const addEventPayload: IAddEventPayload = {
  //                     emails: emails,
  //                     summary: schedule?.summary,
  //                     location: schedule?.location,
  //                     description: schedule?.description
  //                 }

  //                 for (let recurrenceDate of datesWithInAWeek) {
  //                     const recurranceData = {
  //                         fromDate: recurrenceDate.fromDate,
  //                         toDate: recurrenceDate.toDate,
  //                         _id: recurrenceDate._id
  //                     }
  //                     if (recurrenceDate.transactionId || recurrenceDate.amount == 0) {
  //                         continue
  //                     }
  //                     let googleCalenderEvent = {
  //                         link: recurrenceDate.meetLink,
  //                         calenderEventId: recurrenceDate.calenderEventId
  //                     };
  //                     if (!recurrenceDate.calenderEventId) {
  //                         googleCalenderEvent = await GoogleCalendarService.addEventToCalender(therapist._id, addEventPayload, recurranceData, schedule._id);
  //                     }
  //                     const paymentLink = await RazorpayService.createPaymentLink(recurrenceDate?.amount, therapist._id, schedule._id, recurrenceDate._id, client._id);
  //                     if (!paymentLink) {
  //                         continue
  //                     }
  //                     const updateRecurrenceDate = await ScheduleService.updateRecurrenceDate(schedule._id, recurrenceDate._id, paymentLink?.transactionId, googleCalenderEvent.link, googleCalenderEvent.calenderEventId)
  //                     let subject = schedule.summary;
  //                     let htmlTemplate = createScheduleTemplate({
  //                         clientName: client.name,
  //                         therapistName: therapist.name,
  //                         scheduleDate: recurrenceDate?.fromDate,
  //                         meetingLink: schedule.location == "online" ? googleCalenderEvent.link : undefined,
  //                         paymentLink: paymentLink.paymentLink,
  //                         payLater: recurrenceDate.payLater
  //                     })
  //                     const sentEmail = await Mailer2.sendMail(senderData, receiverData, subject, htmlTemplate);
  //                 }
  //             }
  //         }
  //     });
  // }
}
