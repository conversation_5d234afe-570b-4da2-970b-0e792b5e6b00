/**
 * Calendar Sync Configuration
 * 
 * This file contains configuration settings for the enhanced calendar sync functionality:
 * - Sync intervals and limits
 * - Webhook settings
 * - Performance tuning parameters
 * - Feature flags
 */

export interface ICalendarSyncConfig {
  // Session Generation Settings
  sessionGeneration: {
    defaultMaxMonths: number;
    maxSessionsPerSchedule: number;
    autoExtendEnabled: boolean;
    autoExtendThresholdDays: number;
    batchSize: number;
  };

  // Sync Settings
  sync: {
    incrementalSyncInterval: number; // minutes
    fullSyncInterval: number; // hours
    maxEventsPerSync: number;
    syncTimeoutMs: number;
    retryAttempts: number;
    retryDelayMs: number;
  };

  // Webhook Settings
  webhook: {
    enabled: boolean;
    expirationHours: number;
    renewalThresholdHours: number;
    maxWebhooksPerTherapist: number;
    baseUrl: string;
  };

  // Performance Settings
  performance: {
    batchProcessingEnabled: boolean;
    defaultBatchSize: number;
    maxConcurrentSyncs: number;
    rateLimitDelayMs: number;
    cacheEnabled: boolean;
    cacheTtlMinutes: number;
  };

  // Conflict Resolution
  conflictResolution: {
    defaultStrategy: 'skip' | 'warn' | 'force';
    autoResolveEnabled: boolean;
    notifyOnConflicts: boolean;
    maxConflictsBeforeStop: number;
  };

  // Feature Flags
  features: {
    enhancedRescheduleDetection: boolean;
    autoSessionExtension: boolean;
    realTimeSync: boolean;
    performanceMonitoring: boolean;
    advancedConflictResolution: boolean;
  };

  // Monitoring and Logging
  monitoring: {
    enabled: boolean;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
    metricsEnabled: boolean;
    alertsEnabled: boolean;
    performanceThresholds: {
      syncTimeMs: number;
      errorRate: number;
      conflictRate: number;
    };
  };
}

export const CALENDAR_SYNC_CONFIG: ICalendarSyncConfig = {
  sessionGeneration: {
    defaultMaxMonths: 12, // Increased from 3 months
    maxSessionsPerSchedule: 1000,
    autoExtendEnabled: true,
    autoExtendThresholdDays: 30,
    batchSize: 100
  },

  sync: {
    incrementalSyncInterval: 5, // 5 minutes instead of 10 seconds
    fullSyncInterval: 6, // 6 hours
    maxEventsPerSync: 250,
    syncTimeoutMs: 30000, // 30 seconds
    retryAttempts: 3,
    retryDelayMs: 1000
  },

  webhook: {
    enabled: true,
    expirationHours: 24 * 7, // 7 days
    renewalThresholdHours: 2,
    maxWebhooksPerTherapist: 3,
    baseUrl: process.env.WEBHOOK_BASE_URL || 'https://your-domain.com'
  },

  performance: {
    batchProcessingEnabled: true,
    defaultBatchSize: 50,
    maxConcurrentSyncs: 5,
    rateLimitDelayMs: 1000,
    cacheEnabled: true,
    cacheTtlMinutes: 15
  },

  conflictResolution: {
    defaultStrategy: 'warn',
    autoResolveEnabled: false,
    notifyOnConflicts: true,
    maxConflictsBeforeStop: 10
  },

  features: {
    enhancedRescheduleDetection: true,
    autoSessionExtension: true,
    realTimeSync: true,
    performanceMonitoring: true,
    advancedConflictResolution: true
  },

  monitoring: {
    enabled: true,
    logLevel: 'info',
    metricsEnabled: true,
    alertsEnabled: false,
    performanceThresholds: {
      syncTimeMs: 10000, // 10 seconds
      errorRate: 0.05, // 5%
      conflictRate: 0.1 // 10%
    }
  }
};

/**
 * Environment-specific configuration overrides
 */
export const getEnvironmentConfig = (): Partial<ICalendarSyncConfig> => {
  const env = process.env.NODE_ENV || 'development';

  switch (env) {
    case 'production':
      return {
        sync: {
          ...CALENDAR_SYNC_CONFIG.sync,
          incrementalSyncInterval: 3, // More frequent in production
          maxEventsPerSync: 500
        },
        webhook: {
          ...CALENDAR_SYNC_CONFIG.webhook,
          baseUrl: process.env.PRODUCTION_WEBHOOK_URL || CALENDAR_SYNC_CONFIG.webhook.baseUrl
        },
        monitoring: {
          ...CALENDAR_SYNC_CONFIG.monitoring,
          alertsEnabled: true,
          logLevel: 'warn'
        }
      };

    case 'staging':
      return {
        sync: {
          ...CALENDAR_SYNC_CONFIG.sync,
          incrementalSyncInterval: 10
        },
        monitoring: {
          ...CALENDAR_SYNC_CONFIG.monitoring,
          logLevel: 'debug'
        }
      };

    case 'development':
    default:
      return {
        sync: {
          ...CALENDAR_SYNC_CONFIG.sync,
          incrementalSyncInterval: 10, // Less frequent in development
          maxEventsPerSync: 100
        },
        webhook: {
          ...CALENDAR_SYNC_CONFIG.webhook,
          enabled: false // Disable webhooks in development
        },
        monitoring: {
          ...CALENDAR_SYNC_CONFIG.monitoring,
          logLevel: 'debug'
        }
      };
  }
};

/**
 * Get the final configuration with environment overrides
 */
export const getFinalConfig = (): ICalendarSyncConfig => {
  const baseConfig = CALENDAR_SYNC_CONFIG;
  const envOverrides = getEnvironmentConfig();

  return {
    ...baseConfig,
    ...envOverrides,
    sessionGeneration: {
      ...baseConfig.sessionGeneration,
      ...envOverrides.sessionGeneration
    },
    sync: {
      ...baseConfig.sync,
      ...envOverrides.sync
    },
    webhook: {
      ...baseConfig.webhook,
      ...envOverrides.webhook
    },
    performance: {
      ...baseConfig.performance,
      ...envOverrides.performance
    },
    conflictResolution: {
      ...baseConfig.conflictResolution,
      ...envOverrides.conflictResolution
    },
    features: {
      ...baseConfig.features,
      ...envOverrides.features
    },
    monitoring: {
      ...baseConfig.monitoring,
      ...envOverrides.monitoring
    }
  };
};

/**
 * Validate configuration
 */
export const validateConfig = (config: ICalendarSyncConfig): string[] => {
  const errors: string[] = [];

  if (config.sessionGeneration.defaultMaxMonths < 1) {
    errors.push('defaultMaxMonths must be at least 1');
  }

  if (config.sync.incrementalSyncInterval < 1) {
    errors.push('incrementalSyncInterval must be at least 1 minute');
  }

  if (config.webhook.enabled && !config.webhook.baseUrl) {
    errors.push('webhook baseUrl is required when webhooks are enabled');
  }

  if (config.performance.defaultBatchSize < 1) {
    errors.push('defaultBatchSize must be at least 1');
  }

  return errors;
};
