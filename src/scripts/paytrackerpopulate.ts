import moment from "moment";
import { DB } from "../config/DB";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { TherapistDao } from "../lib/dao/therapist.dao";
import { ClientDao } from "../lib/dao/client.dao";
import PayTrackerModel, {
  PaymentTrackerStatusEnum,
  PaymentTrackerTypeEnum,
} from "../models/PayTracker.model";
import { PayTrackerService } from "../services/payTracker.service";
import TransactionModel from "../models/Transaction.model";

async function main() {
  await DB.connect();

  const therapists = await TherapistDao.getAllActiveTherapists();
  for (const therapist of therapists) {
    const currentDate = moment();
    const tillDate = moment().add(1, "week");

    const schedules = await ScheduleDao.getTherapistSchedulesInDateRange(
      therapist._id,
      currentDate,
      tillDate
    );
    for (const schedule of schedules) {
      try {
        console.log("Working on schedule: ", schedule._id);
        const client = await ClientDao.findClientById(schedule.clientId);
        if (!client) {
          continue;
        }
        for (const reccurance of schedule.recurrenceDates) {
          try {
            let payTrackerId = undefined;
            let payTracker = undefined;

            const tracker = await PayTrackerModel.findOne({
              scheduleRecId: reccurance._id,
            });

            if (tracker) {
              console.log("Paytracker already exist");
              continue;
            }

            if (
              moment(reccurance.fromDate).isAfter(currentDate) &&
              moment(reccurance.fromDate).isBefore(tillDate)
            ) {
              let amount = 0;
              if (reccurance.amount) {
                amount = reccurance.amount || 0;
              }
              if (amount == 0) {
                amount = client.defaultSessionAmount
                  ? Number(client.defaultSessionAmount)
                  : 0;
              }

              const linkExist = await TransactionModel.findOne({
                scheduleRecId: reccurance._id,
              });
              const paymentWhen = reccurance.payLater
                ? PaymentTrackerTypeEnum.Post_Session
                : PaymentTrackerTypeEnum.Advance;
              const payload = {
                therapistId: therapist._id,
                scheduleId: schedule._id,
                scheduleRecId: reccurance._id,
                clientId: client._id,
                dueDate: reccurance.fromDate,
                amount: {
                  currency: "INR",
                  value: amount,
                },
                paymentType: paymentWhen,
                status: PaymentTrackerStatusEnum.Still_Pending,
                paymentDate: undefined,
                isDeleted: false,
                tags: [],
                sendRemainder: 0,
                isFine: false,
                cancellationFee: {
                  currency: "INR",
                  value: 0,
                },
                linkExist: linkExist ? true : false,
              };

              payTracker = await PayTrackerService.createPayTracker(payload);
              payTrackerId = payTracker._id;

              await ScheduleDao.updateRecurrenceAmountForPayTracker(
                therapist._id,
                schedule._id,
                reccurance._id,
                amount,
                payTrackerId
              );
            }
          } catch (e) {
            console.log("Error in recurrence: ", reccurance._id);
          }
        }
      } catch (error) {}
    }
  }
  console.log("All therapists updated successfully");
}

main();
