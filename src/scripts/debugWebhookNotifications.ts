/**
 * Debug Webhook Notifications Script
 *
 * This script helps debug webhook notification issues by:
 * 1. Checking webhook setup and status
 * 2. Testing webhook URL accessibility
 * 3. Monitoring notification receipts
 * 4. Providing troubleshooting guidance
 */

import mongoose from 'mongoose';
import { CalendarWebhookService } from '../services/calendarWebhook.service';
import CalendarWebhookModel from '../models/CalendarWebhook.model';
import { GoogleCalendarService } from '../services/googleCalendar.service';
import { TherapistDao } from '../lib/dao/therapist.dao';

// Connect to database
async function connectToDatabase() {
  if (mongoose.connection.readyState === 0) {
    const dbString = process.env.DB_STRING;
    if (!dbString) {
      throw new Error('DB_STRING environment variable is required');
    }
    await mongoose.connect(dbString);
    console.log('Connected to database');
  }
}

export class WebhookDebugger {

  /**
   * Check webhook status for a specific therapist
   */
  static async checkTherapistWebhookStatus(therapistId: string): Promise<void> {
    await connectToDatabase();

    console.log(`\n🔍 Checking webhook status for therapist: ${therapistId}`);
    console.log('='.repeat(60));

    try {
      // Check if therapist exists and has Google Calendar connected
      const therapist = await TherapistDao.getTherapist(therapistId);
      if (!therapist) {
        console.log('❌ Therapist not found');
        return;
      }

      const googleCalendarData = await GoogleCalendarService.findByTherapist(therapistId);
      if (!googleCalendarData) {
        console.log('❌ No Google Calendar connection found for this therapist');
        return;
      }

      console.log('✅ Therapist found with Google Calendar connection');

      // Check active webhooks
      const activeWebhooks = await CalendarWebhookModel.find({
        therapistId,
        isActive: true
      }).sort({ createdAt: -1 });

      console.log(`\n📊 Active Webhooks: ${activeWebhooks.length}`);
      
      if (activeWebhooks.length === 0) {
        console.log('❌ No active webhooks found');
        console.log('💡 Suggestion: Set up a webhook using the /webhook/setup endpoint');
        return;
      }

      for (const webhook of activeWebhooks) {
        console.log(`\n📋 Webhook Details:`);
        console.log(`   Channel ID: ${webhook.channelId}`);
        console.log(`   Resource ID: ${webhook.resourceId}`);
        console.log(`   Created: ${webhook.createdAt}`);
        console.log(`   Expires: ${webhook.expiration}`);
        console.log(`   Is Expired: ${webhook.isExpired()}`);
        console.log(`   Is Expiring Soon: ${webhook.isExpiringSoon()}`);
        console.log(`   Notification Count: ${webhook.notificationCount}`);
        console.log(`   Last Notification: ${webhook.lastNotificationAt || 'Never'}`);
        
        console.log(`\n📈 Sync Statistics:`);
        console.log(`   Total Syncs: ${webhook.syncStatistics.totalSyncs}`);
        console.log(`   Successful: ${webhook.syncStatistics.successfulSyncs}`);
        console.log(`   Failed: ${webhook.syncStatistics.failedSyncs}`);
        console.log(`   Last Sync: ${webhook.syncStatistics.lastSyncAt || 'Never'}`);
        console.log(`   Events Processed: ${webhook.syncStatistics.eventsProcessed}`);

        // Check if webhook is expired or expiring soon
        if (webhook.isExpired()) {
          console.log('⚠️  WARNING: Webhook has expired!');
          console.log('💡 Suggestion: Renew the webhook using the /webhook/renew endpoint');
        } else if (webhook.isExpiringSoon()) {
          console.log('⚠️  WARNING: Webhook is expiring soon!');
          console.log('💡 Suggestion: Consider renewing the webhook');
        }

        // Check notification activity
        if (webhook.notificationCount === 0) {
          console.log('⚠️  WARNING: No notifications received yet');
          console.log('💡 This could indicate:');
          console.log('   - Webhook URL is not accessible from Google servers');
          console.log('   - No calendar changes have been made since webhook setup');
          console.log('   - Firewall blocking incoming requests');
        } else {
          console.log('✅ Webhook is receiving notifications');
          
          // Check recent activity
          const lastNotification = webhook.lastNotificationAt;
          if (lastNotification) {
            const timeSinceLastNotification = Date.now() - lastNotification.getTime();
            const hoursSinceLastNotification = Math.round(timeSinceLastNotification / (1000 * 60 * 60));
            
            if (hoursSinceLastNotification > 24) {
              console.log(`⚠️  Last notification was ${hoursSinceLastNotification} hours ago`);
              console.log('💡 Consider making a test change in Google Calendar');
            }
          }
        }
      }

    } catch (error: any) {
      console.error('❌ Error checking webhook status:', error.message);
    }
  }

  /**
   * Test webhook URL accessibility
   */
  static async testWebhookUrlAccessibility(): Promise<void> {
    console.log(`\n🌐 Testing Webhook URL Accessibility`);
    console.log('='.repeat(60));

    const webhookUrl = `${process.env.WEBHOOK_BASE_URL || process.env.FRONTEND_BASEURL}/api/v1/webhook/calendar`;
    console.log(`Webhook URL: ${webhookUrl}`);

    if (!webhookUrl || webhookUrl.includes('localhost') || webhookUrl.includes('127.0.0.1')) {
      console.log('❌ Webhook URL appears to be localhost - not accessible from Google servers');
      console.log('💡 Suggestions:');
      console.log('   - Use ngrok for development: ngrok http 3000');
      console.log('   - Update WEBHOOK_BASE_URL environment variable');
      console.log('   - Ensure the URL is publicly accessible');
      return;
    }

    if (webhookUrl.includes('ngrok')) {
      console.log('✅ Using ngrok URL - should be accessible');
      console.log('💡 Make sure ngrok is running and the URL is current');
    } else {
      console.log('✅ Using production URL');
    }

    // Additional checks could be added here to actually test the URL
    console.log('\n💡 To test accessibility:');
    console.log('   1. Try accessing the URL from a browser');
    console.log('   2. Use curl or similar tool from external server');
    console.log('   3. Check firewall and security group settings');
  }

  /**
   * Monitor webhook notifications in real-time
   */
  static async monitorWebhookNotifications(therapistId: string, durationMinutes: number = 5): Promise<void> {
    await connectToDatabase();

    console.log(`\n👀 Monitoring webhook notifications for ${durationMinutes} minutes`);
    console.log('='.repeat(60));
    console.log('💡 Make changes in Google Calendar to test webhook notifications');

    const startTime = Date.now();
    const endTime = startTime + (durationMinutes * 60 * 1000);

    // Get initial notification count
    const initialWebhook = await CalendarWebhookModel.findOne({
      therapistId,
      isActive: true
    });

    if (!initialWebhook) {
      console.log('❌ No active webhook found for monitoring');
      return;
    }

    const initialCount = initialWebhook.notificationCount;
    console.log(`Initial notification count: ${initialCount}`);

    const checkInterval = setInterval(async () => {
      try {
        const currentWebhook = await CalendarWebhookModel.findOne({
          therapistId,
          isActive: true
        });

        if (currentWebhook) {
          const currentCount = currentWebhook.notificationCount;
          const newNotifications = currentCount - initialCount;

          if (newNotifications > 0) {
            console.log(`🔔 ${newNotifications} new notification(s) received!`);
            console.log(`   Last notification: ${currentWebhook.lastNotificationAt}`);
            console.log(`   Total syncs: ${currentWebhook.syncStatistics.totalSyncs}`);
          }
        }

        if (Date.now() >= endTime) {
          clearInterval(checkInterval);
          console.log('\n⏰ Monitoring period ended');
          
          const finalWebhook = await CalendarWebhookModel.findOne({
            therapistId,
            isActive: true
          });

          if (finalWebhook) {
            const totalNewNotifications = finalWebhook.notificationCount - initialCount;
            console.log(`📊 Summary: ${totalNewNotifications} notification(s) received during monitoring`);
            
            if (totalNewNotifications === 0) {
              console.log('💡 No notifications received. This could indicate:');
              console.log('   - No calendar changes were made');
              console.log('   - Webhook URL is not accessible');
              console.log('   - Google Calendar is not sending notifications');
            }
          }
        }
      } catch (error: any) {
        console.error('Error during monitoring:', error.message);
      }
    }, 10000); // Check every 10 seconds
  }

  /**
   * Provide troubleshooting guidance
   */
  static async provideTroubleshootingGuidance(): Promise<void> {
    console.log(`\n🔧 Webhook Troubleshooting Guide`);
    console.log('='.repeat(60));

    console.log('\n1. 📋 Check Webhook Setup:');
    console.log('   - Ensure webhook is created and active');
    console.log('   - Verify webhook hasn\'t expired');
    console.log('   - Check webhook URL is publicly accessible');

    console.log('\n2. 🌐 Network Connectivity:');
    console.log('   - Webhook URL must be accessible from Google servers');
    console.log('   - Use ngrok for development (ngrok http 3000)');
    console.log('   - Check firewall and security group settings');
    console.log('   - Ensure HTTPS is used in production');

    console.log('\n3. 📅 Google Calendar Behavior:');
    console.log('   - Google Calendar may not send notifications for all changes');
    console.log('   - Series rescheduling might require specific conditions');
    console.log('   - Some changes might be batched or delayed');

    console.log('\n4. 🔍 Debugging Steps:');
    console.log('   - Monitor server logs when making calendar changes');
    console.log('   - Check webhook notification count increases');
    console.log('   - Test with simple single event changes first');
    console.log('   - Use webhook connectivity test endpoint');

    console.log('\n5. 🚨 Common Issues:');
    console.log('   - Webhook URL contains localhost (not accessible)');
    console.log('   - ngrok tunnel not running or expired');
    console.log('   - Webhook expired and needs renewal');
    console.log('   - Google Calendar permissions insufficient');

    console.log('\n6. 🛠️ Manual Sync Fallback:');
    console.log('   - Use manual sync endpoint as fallback');
    console.log('   - Manual sync can detect changes webhooks missed');
    console.log('   - Consider hybrid approach: webhooks + periodic manual sync');
  }
}

// CLI interface for running debug commands
async function runCommand() {
  const command = process.argv[2];
  const therapistId = process.argv[3];
    try {
      switch (command) {
        case 'check':
          if (!therapistId) {
            console.log('Usage: node -r ts-node/register src/scripts/debugWebhookNotifications.ts check <therapistId>');
            process.exit(1);
          }
          await WebhookDebugger.checkTherapistWebhookStatus(therapistId);
          break;

        case 'test-url':
          await WebhookDebugger.testWebhookUrlAccessibility();
          break;

        case 'monitor':
          if (!therapistId) {
            console.log('Usage: node -r ts-node/register src/scripts/debugWebhookNotifications.ts monitor <therapistId> [minutes]');
            process.exit(1);
          }
          const minutes = parseInt(process.argv[4]) || 5;
          await WebhookDebugger.monitorWebhookNotifications(therapistId, minutes);
          break;

        case 'guide':
          await WebhookDebugger.provideTroubleshootingGuidance();
          break;

        default:
          console.log('Available commands:');
          console.log('  check <therapistId>     - Check webhook status for therapist');
          console.log('  test-url               - Test webhook URL accessibility');
          console.log('  monitor <therapistId>  - Monitor notifications for 5 minutes');
          console.log('  guide                  - Show troubleshooting guide');
      }
    } catch (error: any) {
      console.error('Error running command:', error.message);
      process.exit(1);
    } finally {
      if (mongoose.connection.readyState === 1) {
        await mongoose.connection.close();
      }
      process.exit(0);
    }
}

if (require.main === module) {
  runCommand();
}
