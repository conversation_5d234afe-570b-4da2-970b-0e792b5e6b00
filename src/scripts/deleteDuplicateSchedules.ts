import { DB } from '../config/DB';
import calendarEventModel from '../models/calendarEvent.model';
import ScheduleModel from '../models/Schedule.model';

async function deleteDuplicateSchedules() {
    await DB.connect();

    const calendarEvents = await calendarEventModel.find({}, "id");
    const allCalendarEvents = await calendarEvents.map((event) => event.id);
    const uniqueCalendarEventIds = await Array.from(new Set(allCalendarEvents));
    for(let eventId of uniqueCalendarEventIds){
        const calendarEventsByid = await calendarEventModel.find({id: eventId});
        if(calendarEvents.length > 0){
            const calendarEventsToBeRemoved: any = await calendarEventsByid.shift();
            for(let calendarEvent of calendarEventsByid ){
                await ScheduleModel.deleteOne({"recurrenceDates.calenderEventId": calendarEvent._id})
                await calendarEventModel.deleteOne({_id: calendarEvent._id})
            }
        }
        else{
            continue
        }
    }
}



deleteDuplicateSchedules();
